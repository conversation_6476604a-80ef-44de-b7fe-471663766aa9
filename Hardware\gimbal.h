#ifndef __GIMBAL_H
#define __GIMBAL_H

#ifdef __cplusplus
extern "C" {
#endif

#include "stm32f10x.h"
#include "servo.h"

// ��̨����ṹ��
typedef struct {
    int16_t x;  // X������
    int16_t y;  // Y������
} GimbalPosition;

// ��̨��ʼ��
void Gimbal_Init(void);

// ============== ��һ����̨���� ==============
void Gimbal_SetTargetPosition(int16_t x, int16_t y);
void Gimbal_UpdatePosition(void);
GimbalPosition Gimbal_GetCurrentPosition(void);
GimbalPosition Gimbal_GetTargetPosition(void);
void Gimbal_MoveXPlus(void);
void Gimbal_MoveXMinus(void);
void Gimbal_MoveYPlus(void);
void Gimbal_MoveYMinus(void);
void Gimbal_AdjustToRedTarget(void);     // 控制红色激光瞄准目标
void Gimbal_AdjustToCenterTarget(void);  // 控制云台追踪目标中心点（方案A）


#ifdef __cplusplus
}
#endif

#endif /* __GIMBAL_H */

