Dependencies for Project 'Project', Target 'Target 1': (DO NOT MODIFY !)
CompilerVersion: 6220000::V6.22::ARMCLANG
F (.\Library\stm32f10x_adc.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_adc.o -MMD)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\misc.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/misc.o -MMD)
I (Library\misc.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
F (.\Library\stm32f10x_bkp.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_bkp.o -MMD)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_can.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_can.o -MMD)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_cec.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_cec.o -MMD)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_crc.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_crc.o -MMD)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_dac.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_dac.o -MMD)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_dbgmcu.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_dbgmcu.o -MMD)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_dma.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_dma.o -MMD)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_exti.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_exti.o -MMD)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_flash.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_flash.o -MMD)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_fsmc.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_fsmc.o -MMD)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_gpio.c)(0x4D79EEC6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_gpio.o -MMD)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_i2c.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_i2c.o -MMD)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_iwdg.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_iwdg.o -MMD)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_pwr.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_pwr.o -MMD)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_rcc.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_rcc.o -MMD)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_rtc.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_rtc.o -MMD)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_sdio.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_sdio.o -MMD)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_spi.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_spi.o -MMD)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_tim.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_tim.o -MMD)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_usart.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_usart.o -MMD)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Library\stm32f10x_wwdg.c)(0x4D783BB4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_wwdg.o -MMD)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Start\stm32f10x.h)(0x4D783CB4)()
F (.\Start\system_stm32f10x.c)(0x4D783CB0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/system_stm32f10x.o -MMD)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\Start\system_stm32f10x.h)(0x4D783CAA)()
F (.\Start\core_cm3.h)(0x4D523B58)()
F (.\Start\core_cm3.c)(0x6888386B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/core_cm3.o -MMD)
F (.\Start\startup_stm32f10x_hd.s)(0x4D783CDE)(--target=arm-arm-none-eabi -mcpu=cortex-m3 -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-Wa,armasm,--pd,"__UVISION_VERSION SETA 541" -Wa,armasm,--pd,"STM32F10X_HD SETA 1"

-o ./objects/startup_stm32f10x_hd.o)
F (.\User\main.c)(0x688836A1)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/main.o -MMD)
I (Hardware\gimbal.h)(0x68883694)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
I (Hardware\servo.h)(0x688492CC)
I (System\usart1.h)(0x68883642)
F (.\User\stm32f10x_conf.h)(0x4D99A59E)()
F (.\User\stm32f10x_it.c)(0x68883651)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/stm32f10x_it.o -MMD)
I (User\stm32f10x_it.h)(0x4D99A59E)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
I (System\usart1.h)(0x68883642)
F (.\User\stm32f10x_it.h)(0x4D99A59E)()
F (.\System\pwm1.c)(0x687AEC04)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/pwm1.o -MMD)
I (System\pwm1.h)(0x687AEBAE)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\System\pwm1.h)(0x687AEBAE)()
F (.\System\pwm2.c)(0x687C9A0A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/pwm2.o -MMD)
I (System\pwm2.h)(0x687AEBAE)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
F (.\System\pwm2.h)(0x687AEBAE)()
F (.\System\usart1.c)(0x688836FB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/usart1.o -MMD)
I (System\usart1.h)(0x68883642)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
I (System\uart_vision.h)(0x687B59F2)
I (Hardware\uart_gimbal.h)(0x68858B18)
F (.\System\usart1.h)(0x68883642)()
F (.\System\uart_vision.c)(0x687B59F2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/uart_vision.o -MMD)
I (System\uart_vision.h)(0x687B59F2)
F (.\System\uart_vision.h)(0x687B59F2)()
F (.\System\timer7.c)(0x687F511A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/timer7.o -MMD)
I (System\timer7.h)(0x687F4FEA)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
I (Hardware\motor_control.h)(0x687B0436)
I (System\pid.h)(0x687456F8)
F (.\System\timer7.h)(0x687F4FEA)()
F (.\Hardware\OLED.c)(0x687DE92E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/oled.o -MMD)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
I (Hardware\OLED.h)(0x6714EDC0)
I (Hardware\OLED_Data.h)(0x6714EE44)
F (.\Hardware\OLED.h)(0x6714EDC0)()
F (.\Hardware\OLED_Data.c)(0x6714EE46)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/oled_data.o -MMD)
I (Hardware\OLED_Data.h)(0x6714EE44)
F (.\Hardware\OLED_Data.h)(0x6714EE44)()
F (.\Hardware\encoder.c)(0x6880963C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/encoder.o -MMD)
I (Hardware\encoder.h)(0x687DF01C)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
I (Hardware\OLED.h)(0x6714EDC0)
I (Hardware\OLED_Data.h)(0x6714EE44)
I (Hardware\motor_control.h)(0x687B0436)
I (System\pid.h)(0x687456F8)
I (Hardware\servo.h)(0x688492CC)
F (.\Hardware\encoder.h)(0x687DF01C)()
F (.\System\pid.c)(0x687456F8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/pid.o -MMD)
I (System\pid.h)(0x687456F8)
F (.\System\pid.h)(0x687456F8)()
F (.\Hardware\servo.c)(0x688492CC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/servo.o -MMD)
I (Hardware\servo.h)(0x688492CC)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
I (System\pwm2.h)(0x687AEBAE)
I (System\uart_vision.h)(0x687B59F2)
F (.\Hardware\servo.h)(0x688492CC)()
F (.\Hardware\motor_control.c)(0x6880969A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/motor_control.o -MMD)
I (Hardware\motor_control.h)(0x687B0436)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
I (System\pid.h)(0x687456F8)
I (System\pwm1.h)(0x687AEBAE)
I (Hardware\encoder.h)(0x687DF01C)
F (.\Hardware\motor_control.h)(0x687B0436)()
F (.\Hardware\gimbal.c)(0x68883709)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./Start -I ./User -I ./Library -I ./Hardware -I ./System

-I"D:/Keil5 MDK/Keil/STM32F1xx_DFP/2.2.0/Device/Include"

-D__UVISION_VERSION="541" -DSTM32F10X_HD -DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD

-o ./objects/gimbal.o -MMD)
I (Hardware\gimbal.h)(0x68883694)
I (Start\stm32f10x.h)(0x4D783CB4)
I (Start\core_cm3.h)(0x4D523B58)
I (Start\system_stm32f10x.h)(0x4D783CAA)
I (User\stm32f10x_conf.h)(0x4D99A59E)
I (Library\stm32f10x_adc.h)(0x4D783BB4)
I (Library\stm32f10x_bkp.h)(0x4D783BB4)
I (Library\stm32f10x_can.h)(0x4D783BB4)
I (Library\stm32f10x_cec.h)(0x4D783BB4)
I (Library\stm32f10x_crc.h)(0x4D783BB4)
I (Library\stm32f10x_dac.h)(0x4D783BB4)
I (Library\stm32f10x_dbgmcu.h)(0x4D783BB4)
I (Library\stm32f10x_dma.h)(0x4D783BB4)
I (Library\stm32f10x_exti.h)(0x4D783BB4)
I (Library\stm32f10x_flash.h)(0x4D783BB4)
I (Library\stm32f10x_fsmc.h)(0x4D783BB4)
I (Library\stm32f10x_gpio.h)(0x4D783BB4)
I (Library\stm32f10x_i2c.h)(0x4D783BB4)
I (Library\stm32f10x_iwdg.h)(0x4D783BB4)
I (Library\stm32f10x_pwr.h)(0x4D783BB4)
I (Library\stm32f10x_rcc.h)(0x4D783BB4)
I (Library\stm32f10x_rtc.h)(0x4D783BB4)
I (Library\stm32f10x_sdio.h)(0x4D783BB4)
I (Library\stm32f10x_spi.h)(0x4D783BB4)
I (Library\stm32f10x_tim.h)(0x4D783BB4)
I (Library\stm32f10x_usart.h)(0x4D783BB4)
I (Library\stm32f10x_wwdg.h)(0x4D783BB4)
I (Library\misc.h)(0x4D783BB4)
I (Hardware\servo.h)(0x688492CC)
I (System\usart1.h)(0x68883642)
F (.\Hardware\gimbal.h)(0x68883694)()
