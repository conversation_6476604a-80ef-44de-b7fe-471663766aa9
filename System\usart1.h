#ifndef __USART1_H
#define __USART1_H

#ifdef __cplusplus
extern "C" {
#endif

#include "stm32f10x.h"

// ����ȫ�ֱ���
extern int16_t redx, redy;
extern int16_t centerx, centery;

void USART1_Init(uint32_t baudrate);
void USART1_SendChar(char ch);
void USART1_SendString(const char *str);
void USART1_IRQHandler(void);
void USART1_ProcessReceivedData(uint8_t data);  // 新增数据处理函数
void parse_received_data(char* line);

#ifdef __cplusplus
}
#endif

#endif /* __USART1_H */
