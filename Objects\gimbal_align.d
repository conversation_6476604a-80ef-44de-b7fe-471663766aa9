.\objects\gimbal_align.o: Hardware\gimbal_align.c
.\objects\gimbal_align.o: Hardware\gimbal_align.h
.\objects\gimbal_align.o: .\Start\stm32f10x.h
.\objects\gimbal_align.o: .\Start\core_cm3.h
.\objects\gimbal_align.o: D:\Keil5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\gimbal_align.o: .\Start\system_stm32f10x.h
.\objects\gimbal_align.o: .\User\stm32f10x_conf.h
.\objects\gimbal_align.o: .\Library\stm32f10x_adc.h
.\objects\gimbal_align.o: .\Start\stm32f10x.h
.\objects\gimbal_align.o: .\Library\stm32f10x_bkp.h
.\objects\gimbal_align.o: .\Library\stm32f10x_can.h
.\objects\gimbal_align.o: .\Library\stm32f10x_cec.h
.\objects\gimbal_align.o: .\Library\stm32f10x_crc.h
.\objects\gimbal_align.o: .\Library\stm32f10x_dac.h
.\objects\gimbal_align.o: .\Library\stm32f10x_dbgmcu.h
.\objects\gimbal_align.o: .\Library\stm32f10x_dma.h
.\objects\gimbal_align.o: .\Library\stm32f10x_exti.h
.\objects\gimbal_align.o: .\Library\stm32f10x_flash.h
.\objects\gimbal_align.o: .\Library\stm32f10x_fsmc.h
.\objects\gimbal_align.o: .\Library\stm32f10x_gpio.h
.\objects\gimbal_align.o: .\Library\stm32f10x_i2c.h
.\objects\gimbal_align.o: .\Library\stm32f10x_iwdg.h
.\objects\gimbal_align.o: .\Library\stm32f10x_pwr.h
.\objects\gimbal_align.o: .\Library\stm32f10x_rcc.h
.\objects\gimbal_align.o: .\Library\stm32f10x_rtc.h
.\objects\gimbal_align.o: .\Library\stm32f10x_sdio.h
.\objects\gimbal_align.o: .\Library\stm32f10x_spi.h
.\objects\gimbal_align.o: .\Library\stm32f10x_tim.h
.\objects\gimbal_align.o: .\Library\stm32f10x_usart.h
.\objects\gimbal_align.o: .\Library\stm32f10x_wwdg.h
.\objects\gimbal_align.o: .\Library\misc.h
.\objects\gimbal_align.o: Hardware\gimbal.h
.\objects\gimbal_align.o: Hardware\servo.h
.\objects\gimbal_align.o: D:\Keil5\ARM\ARMCC\Bin\..\include\math.h
