#include "gimbal.h"
#include "usart1.h"

// �򵥵ĺ�����ʱ����
void Delay_ms(uint32_t ms) {
    uint32_t i, j;
    for(i = 0; i < ms; i++) {
        for(j = 0; j < 7200; j++);  // ����ϵͳʱ�ӵ������ֵ
    }
}

int main(void)
{
	  // ��ʼ��
	SystemInit();
    Gimbal_Init();
    USART1_Init(115200);
	
	 while(1) {

       // 控制云台1追踪目标中心点（方案A）
        Gimbal_AdjustToCenterTarget();  // 使用新的控制函数
        Gimbal_UpdatePosition();
		Delay_ms(100);  // 减少延时，提高响应速度

//        // 控制云台2（绿色激光）
//        Gimbal2_AdjustToGreenTarget();
//        Gimbal2_UpdatePosition();
//		Delay_ms(500);
		 }
}
