#include "gimbal.h"
#include "usart1.h"

// �򵥵ĺ�����ʱ����
void Delay_ms(uint32_t ms) {
    uint32_t i, j;
    for(i = 0; i < ms; i++) {
        for(j = 0; j < 7200; j++);  // ����ϵͳʱ�ӵ������ֵ
    }
}

int main(void)
{
	  // ��ʼ��
	SystemInit();
    Gimbal_Init();
    USART1_Init(115200);
	
	 while(1) {
             
       // ������̨1�����ƺ�ɫ���⣩
		Gimbal_SetTargetPosition(centerx,centery);
        Gimbal_AdjustToRedTarget();
        Gimbal_UpdatePosition();
		Delay_ms(500);
        
//        // ������̨2��������ɫ���⣩
//        Gimbal2_AdjustToGreenTarget();
//        Gimbal2_UpdatePosition();
//		Delay_ms(500);	
		 }
}
