Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    stm32f10x_adc.o(.text.ADC_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_adc.o(.ARM.exidx.text.ADC_DeInit) refers to stm32f10x_adc.o(.text.ADC_DeInit) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_Init) refers to stm32f10x_adc.o(.text.ADC_Init) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_StructInit) refers to stm32f10x_adc.o(.text.ADC_StructInit) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_Cmd) refers to stm32f10x_adc.o(.text.ADC_Cmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_DMACmd) refers to stm32f10x_adc.o(.text.ADC_DMACmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ITConfig) refers to stm32f10x_adc.o(.text.ADC_ITConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ResetCalibration) refers to stm32f10x_adc.o(.text.ADC_ResetCalibration) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetResetCalibrationStatus) refers to stm32f10x_adc.o(.text.ADC_GetResetCalibrationStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_StartCalibration) refers to stm32f10x_adc.o(.text.ADC_StartCalibration) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetCalibrationStatus) refers to stm32f10x_adc.o(.text.ADC_GetCalibrationStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_SoftwareStartConvCmd) refers to stm32f10x_adc.o(.text.ADC_SoftwareStartConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetSoftwareStartConvStatus) refers to stm32f10x_adc.o(.text.ADC_GetSoftwareStartConvStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_DiscModeChannelCountConfig) refers to stm32f10x_adc.o(.text.ADC_DiscModeChannelCountConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_DiscModeCmd) refers to stm32f10x_adc.o(.text.ADC_DiscModeCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_RegularChannelConfig) refers to stm32f10x_adc.o(.text.ADC_RegularChannelConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigConvCmd) refers to stm32f10x_adc.o(.text.ADC_ExternalTrigConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetConversionValue) refers to stm32f10x_adc.o(.text.ADC_GetConversionValue) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetDualModeConversionValue) refers to stm32f10x_adc.o(.text.ADC_GetDualModeConversionValue) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_AutoInjectedConvCmd) refers to stm32f10x_adc.o(.text.ADC_AutoInjectedConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedDiscModeCmd) refers to stm32f10x_adc.o(.text.ADC_InjectedDiscModeCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigInjectedConvConfig) refers to stm32f10x_adc.o(.text.ADC_ExternalTrigInjectedConvConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigInjectedConvCmd) refers to stm32f10x_adc.o(.text.ADC_ExternalTrigInjectedConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_SoftwareStartInjectedConvCmd) refers to stm32f10x_adc.o(.text.ADC_SoftwareStartInjectedConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetSoftwareStartInjectedConvCmdStatus) refers to stm32f10x_adc.o(.text.ADC_GetSoftwareStartInjectedConvCmdStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedChannelConfig) refers to stm32f10x_adc.o(.text.ADC_InjectedChannelConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedSequencerLengthConfig) refers to stm32f10x_adc.o(.text.ADC_InjectedSequencerLengthConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_SetInjectedOffset) refers to stm32f10x_adc.o(.text.ADC_SetInjectedOffset) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetInjectedConversionValue) refers to stm32f10x_adc.o(.text.ADC_GetInjectedConversionValue) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogCmd) refers to stm32f10x_adc.o(.text.ADC_AnalogWatchdogCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogThresholdsConfig) refers to stm32f10x_adc.o(.text.ADC_AnalogWatchdogThresholdsConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogSingleChannelConfig) refers to stm32f10x_adc.o(.text.ADC_AnalogWatchdogSingleChannelConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_TempSensorVrefintCmd) refers to stm32f10x_adc.o(.text.ADC_TempSensorVrefintCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetFlagStatus) refers to stm32f10x_adc.o(.text.ADC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ClearFlag) refers to stm32f10x_adc.o(.text.ADC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetITStatus) refers to stm32f10x_adc.o(.text.ADC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ClearITPendingBit) refers to stm32f10x_adc.o(.text.ADC_ClearITPendingBit) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.NVIC_PriorityGroupConfig) refers to misc.o(.text.NVIC_PriorityGroupConfig) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.NVIC_Init) refers to misc.o(.text.NVIC_Init) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.NVIC_SetVectorTable) refers to misc.o(.text.NVIC_SetVectorTable) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.NVIC_SystemLPConfig) refers to misc.o(.text.NVIC_SystemLPConfig) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.SysTick_CLKSourceConfig) refers to misc.o(.text.SysTick_CLKSourceConfig) for [Anonymous Symbol]
    stm32f10x_bkp.o(.text.BKP_DeInit) refers to stm32f10x_rcc.o(.text.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_DeInit) refers to stm32f10x_bkp.o(.text.BKP_DeInit) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_TamperPinLevelConfig) refers to stm32f10x_bkp.o(.text.BKP_TamperPinLevelConfig) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_TamperPinCmd) refers to stm32f10x_bkp.o(.text.BKP_TamperPinCmd) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_ITConfig) refers to stm32f10x_bkp.o(.text.BKP_ITConfig) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_RTCOutputConfig) refers to stm32f10x_bkp.o(.text.BKP_RTCOutputConfig) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_SetRTCCalibrationValue) refers to stm32f10x_bkp.o(.text.BKP_SetRTCCalibrationValue) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_WriteBackupRegister) refers to stm32f10x_bkp.o(.text.BKP_WriteBackupRegister) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_ReadBackupRegister) refers to stm32f10x_bkp.o(.text.BKP_ReadBackupRegister) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_GetFlagStatus) refers to stm32f10x_bkp.o(.text.BKP_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_ClearFlag) refers to stm32f10x_bkp.o(.text.BKP_ClearFlag) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_GetITStatus) refers to stm32f10x_bkp.o(.text.BKP_GetITStatus) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_ClearITPendingBit) refers to stm32f10x_bkp.o(.text.BKP_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_can.o(.text.CAN_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(.ARM.exidx.text.CAN_DeInit) refers to stm32f10x_can.o(.text.CAN_DeInit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_Init) refers to stm32f10x_can.o(.text.CAN_Init) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_FilterInit) refers to stm32f10x_can.o(.text.CAN_FilterInit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_StructInit) refers to stm32f10x_can.o(.text.CAN_StructInit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_SlaveStartBank) refers to stm32f10x_can.o(.text.CAN_SlaveStartBank) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_DBGFreeze) refers to stm32f10x_can.o(.text.CAN_DBGFreeze) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_TTComModeCmd) refers to stm32f10x_can.o(.text.CAN_TTComModeCmd) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_Transmit) refers to stm32f10x_can.o(.text.CAN_Transmit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_TransmitStatus) refers to stm32f10x_can.o(.text.CAN_TransmitStatus) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_CancelTransmit) refers to stm32f10x_can.o(.text.CAN_CancelTransmit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_Receive) refers to stm32f10x_can.o(.text.CAN_Receive) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_FIFORelease) refers to stm32f10x_can.o(.text.CAN_FIFORelease) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_MessagePending) refers to stm32f10x_can.o(.text.CAN_MessagePending) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_OperatingModeRequest) refers to stm32f10x_can.o(.text.CAN_OperatingModeRequest) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_Sleep) refers to stm32f10x_can.o(.text.CAN_Sleep) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_WakeUp) refers to stm32f10x_can.o(.text.CAN_WakeUp) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetLastErrorCode) refers to stm32f10x_can.o(.text.CAN_GetLastErrorCode) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetReceiveErrorCounter) refers to stm32f10x_can.o(.text.CAN_GetReceiveErrorCounter) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetLSBTransmitErrorCounter) refers to stm32f10x_can.o(.text.CAN_GetLSBTransmitErrorCounter) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_ITConfig) refers to stm32f10x_can.o(.text.CAN_ITConfig) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetFlagStatus) refers to stm32f10x_can.o(.text.CAN_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_ClearFlag) refers to stm32f10x_can.o(.text.CAN_ClearFlag) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetITStatus) refers to stm32f10x_can.o(.text.CAN_GetITStatus) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_ClearITPendingBit) refers to stm32f10x_can.o(.text.CAN_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_cec.o(.text.CEC_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_cec.o(.ARM.exidx.text.CEC_DeInit) refers to stm32f10x_cec.o(.text.CEC_DeInit) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_Init) refers to stm32f10x_cec.o(.text.CEC_Init) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_Cmd) refers to stm32f10x_cec.o(.text.CEC_Cmd) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_ITConfig) refers to stm32f10x_cec.o(.text.CEC_ITConfig) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_OwnAddressConfig) refers to stm32f10x_cec.o(.text.CEC_OwnAddressConfig) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_SetPrescaler) refers to stm32f10x_cec.o(.text.CEC_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_SendDataByte) refers to stm32f10x_cec.o(.text.CEC_SendDataByte) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_ReceiveDataByte) refers to stm32f10x_cec.o(.text.CEC_ReceiveDataByte) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_StartOfMessage) refers to stm32f10x_cec.o(.text.CEC_StartOfMessage) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_EndOfMessageCmd) refers to stm32f10x_cec.o(.text.CEC_EndOfMessageCmd) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_GetFlagStatus) refers to stm32f10x_cec.o(.text.CEC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_ClearFlag) refers to stm32f10x_cec.o(.text.CEC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_GetITStatus) refers to stm32f10x_cec.o(.text.CEC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_ClearITPendingBit) refers to stm32f10x_cec.o(.text.CEC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_ResetDR) refers to stm32f10x_crc.o(.text.CRC_ResetDR) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_CalcCRC) refers to stm32f10x_crc.o(.text.CRC_CalcCRC) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_CalcBlockCRC) refers to stm32f10x_crc.o(.text.CRC_CalcBlockCRC) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_GetCRC) refers to stm32f10x_crc.o(.text.CRC_GetCRC) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_SetIDRegister) refers to stm32f10x_crc.o(.text.CRC_SetIDRegister) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_GetIDRegister) refers to stm32f10x_crc.o(.text.CRC_GetIDRegister) for [Anonymous Symbol]
    stm32f10x_dac.o(.text.DAC_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(.ARM.exidx.text.DAC_DeInit) refers to stm32f10x_dac.o(.text.DAC_DeInit) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_Init) refers to stm32f10x_dac.o(.text.DAC_Init) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_StructInit) refers to stm32f10x_dac.o(.text.DAC_StructInit) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_Cmd) refers to stm32f10x_dac.o(.text.DAC_Cmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_DMACmd) refers to stm32f10x_dac.o(.text.DAC_DMACmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_SoftwareTriggerCmd) refers to stm32f10x_dac.o(.text.DAC_SoftwareTriggerCmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_DualSoftwareTriggerCmd) refers to stm32f10x_dac.o(.text.DAC_DualSoftwareTriggerCmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_WaveGenerationCmd) refers to stm32f10x_dac.o(.text.DAC_WaveGenerationCmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_SetChannel1Data) refers to stm32f10x_dac.o(.text.DAC_SetChannel1Data) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_SetChannel2Data) refers to stm32f10x_dac.o(.text.DAC_SetChannel2Data) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_SetDualChannelData) refers to stm32f10x_dac.o(.text.DAC_SetDualChannelData) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_GetDataOutputValue) refers to stm32f10x_dac.o(.text.DAC_GetDataOutputValue) for [Anonymous Symbol]
    stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_GetREVID) refers to stm32f10x_dbgmcu.o(.text.DBGMCU_GetREVID) for [Anonymous Symbol]
    stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_GetDEVID) refers to stm32f10x_dbgmcu.o(.text.DBGMCU_GetDEVID) for [Anonymous Symbol]
    stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_Config) refers to stm32f10x_dbgmcu.o(.text.DBGMCU_Config) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_DeInit) refers to stm32f10x_dma.o(.text.DMA_DeInit) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_Init) refers to stm32f10x_dma.o(.text.DMA_Init) for [Anonymous Symbol]
    stm32f10x_dma.o(.text.DMA_StructInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32f10x_dma.o(.ARM.exidx.text.DMA_StructInit) refers to stm32f10x_dma.o(.text.DMA_StructInit) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_Cmd) refers to stm32f10x_dma.o(.text.DMA_Cmd) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_ITConfig) refers to stm32f10x_dma.o(.text.DMA_ITConfig) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_SetCurrDataCounter) refers to stm32f10x_dma.o(.text.DMA_SetCurrDataCounter) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_GetCurrDataCounter) refers to stm32f10x_dma.o(.text.DMA_GetCurrDataCounter) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_GetFlagStatus) refers to stm32f10x_dma.o(.text.DMA_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_ClearFlag) refers to stm32f10x_dma.o(.text.DMA_ClearFlag) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_GetITStatus) refers to stm32f10x_dma.o(.text.DMA_GetITStatus) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_ClearITPendingBit) refers to stm32f10x_dma.o(.text.DMA_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_DeInit) refers to stm32f10x_exti.o(.text.EXTI_DeInit) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_Init) refers to stm32f10x_exti.o(.text.EXTI_Init) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_StructInit) refers to stm32f10x_exti.o(.text.EXTI_StructInit) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_GenerateSWInterrupt) refers to stm32f10x_exti.o(.text.EXTI_GenerateSWInterrupt) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_GetFlagStatus) refers to stm32f10x_exti.o(.text.EXTI_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_ClearFlag) refers to stm32f10x_exti.o(.text.EXTI_ClearFlag) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_GetITStatus) refers to stm32f10x_exti.o(.text.EXTI_GetITStatus) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_ClearITPendingBit) refers to stm32f10x_exti.o(.text.EXTI_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_SetLatency) refers to stm32f10x_flash.o(.text.FLASH_SetLatency) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_HalfCycleAccessCmd) refers to stm32f10x_flash.o(.text.FLASH_HalfCycleAccessCmd) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_PrefetchBufferCmd) refers to stm32f10x_flash.o(.text.FLASH_PrefetchBufferCmd) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_Unlock) refers to stm32f10x_flash.o(.text.FLASH_Unlock) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_UnlockBank1) refers to stm32f10x_flash.o(.text.FLASH_UnlockBank1) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_Lock) refers to stm32f10x_flash.o(.text.FLASH_Lock) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_LockBank1) refers to stm32f10x_flash.o(.text.FLASH_LockBank1) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ErasePage) refers to stm32f10x_flash.o(.text.FLASH_ErasePage) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseAllPages) refers to stm32f10x_flash.o(.text.FLASH_EraseAllPages) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(.text.FLASH_EraseAllBank1Pages) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(.text.FLASH_WaitForLastBank1Operation) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(.text.FLASH_EraseOptionBytes) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetReadOutProtectionStatus) refers to stm32f10x_flash.o(.text.FLASH_GetReadOutProtectionStatus) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramWord) refers to stm32f10x_flash.o(.text.FLASH_ProgramWord) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(.text.FLASH_ProgramHalfWord) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(.text.FLASH_ProgramOptionByteData) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(.text.FLASH_EnableWriteProtection) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(.text.FLASH_ReadOutProtection) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(.text.FLASH_UserOptionByteConfig) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetUserOptionByte) refers to stm32f10x_flash.o(.text.FLASH_GetUserOptionByte) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetWriteProtectionOptionByte) refers to stm32f10x_flash.o(.text.FLASH_GetWriteProtectionOptionByte) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetPrefetchBufferStatus) refers to stm32f10x_flash.o(.text.FLASH_GetPrefetchBufferStatus) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ITConfig) refers to stm32f10x_flash.o(.text.FLASH_ITConfig) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetFlagStatus) refers to stm32f10x_flash.o(.text.FLASH_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ClearFlag) refers to stm32f10x_flash.o(.text.FLASH_ClearFlag) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetStatus) refers to stm32f10x_flash.o(.text.FLASH_GetStatus) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetBank1Status) refers to stm32f10x_flash.o(.text.FLASH_GetBank1Status) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMDeInit) refers to stm32f10x_fsmc.o(.text.FSMC_NORSRAMDeInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDDeInit) refers to stm32f10x_fsmc.o(.text.FSMC_NANDDeInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDDeInit) refers to stm32f10x_fsmc.o(.text.FSMC_PCCARDDeInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMInit) refers to stm32f10x_fsmc.o(.text.FSMC_NORSRAMInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDInit) refers to stm32f10x_fsmc.o(.text.FSMC_NANDInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDInit) refers to stm32f10x_fsmc.o(.text.FSMC_PCCARDInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMStructInit) refers to stm32f10x_fsmc.o(.text.FSMC_NORSRAMStructInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDStructInit) refers to stm32f10x_fsmc.o(.text.FSMC_NANDStructInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDStructInit) refers to stm32f10x_fsmc.o(.text.FSMC_PCCARDStructInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMCmd) refers to stm32f10x_fsmc.o(.text.FSMC_NORSRAMCmd) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDCmd) refers to stm32f10x_fsmc.o(.text.FSMC_NANDCmd) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDCmd) refers to stm32f10x_fsmc.o(.text.FSMC_PCCARDCmd) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDECCCmd) refers to stm32f10x_fsmc.o(.text.FSMC_NANDECCCmd) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetECC) refers to stm32f10x_fsmc.o(.text.FSMC_GetECC) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ITConfig) refers to stm32f10x_fsmc.o(.text.FSMC_ITConfig) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetFlagStatus) refers to stm32f10x_fsmc.o(.text.FSMC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ClearFlag) refers to stm32f10x_fsmc.o(.text.FSMC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetITStatus) refers to stm32f10x_fsmc.o(.text.FSMC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ClearITPendingBit) refers to stm32f10x_fsmc.o(.text.FSMC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.text.GPIO_DeInit) refers to stm32f10x_gpio.o(.rodata..Lswitch.table.GPIO_DeInit.1) for .Lswitch.table.GPIO_DeInit.1
    stm32f10x_gpio.o(.text.GPIO_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_DeInit) refers to stm32f10x_gpio.o(.text.GPIO_DeInit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.text.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_AFIODeInit) refers to stm32f10x_gpio.o(.text.GPIO_AFIODeInit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_StructInit) refers to stm32f10x_gpio.o(.text.GPIO_StructInit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadInputDataBit) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadInputData) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputData) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadOutputDataBit) refers to stm32f10x_gpio.o(.text.GPIO_ReadOutputDataBit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadOutputData) refers to stm32f10x_gpio.o(.text.GPIO_ReadOutputData) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_SetBits) refers to stm32f10x_gpio.o(.text.GPIO_SetBits) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ResetBits) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_WriteBit) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_Write) refers to stm32f10x_gpio.o(.text.GPIO_Write) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_PinLockConfig) refers to stm32f10x_gpio.o(.text.GPIO_PinLockConfig) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EventOutputConfig) refers to stm32f10x_gpio.o(.text.GPIO_EventOutputConfig) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EventOutputCmd) refers to stm32f10x_gpio.o(.text.GPIO_EventOutputCmd) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_PinRemapConfig) refers to stm32f10x_gpio.o(.text.GPIO_PinRemapConfig) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EXTILineConfig) refers to stm32f10x_gpio.o(.text.GPIO_EXTILineConfig) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ETH_MediaInterfaceConfig) refers to stm32f10x_gpio.o(.text.GPIO_ETH_MediaInterfaceConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.text.I2C_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_DeInit) refers to stm32f10x_i2c.o(.text.I2C_DeInit) for [Anonymous Symbol]
    stm32f10x_i2c.o(.text.I2C_Init) refers to stm32f10x_rcc.o(.text.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_Init) refers to stm32f10x_i2c.o(.text.I2C_Init) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_StructInit) refers to stm32f10x_i2c.o(.text.I2C_StructInit) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_Cmd) refers to stm32f10x_i2c.o(.text.I2C_Cmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_DMACmd) refers to stm32f10x_i2c.o(.text.I2C_DMACmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_DMALastTransferCmd) refers to stm32f10x_i2c.o(.text.I2C_DMALastTransferCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GenerateSTART) refers to stm32f10x_i2c.o(.text.I2C_GenerateSTART) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GenerateSTOP) refers to stm32f10x_i2c.o(.text.I2C_GenerateSTOP) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_AcknowledgeConfig) refers to stm32f10x_i2c.o(.text.I2C_AcknowledgeConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_OwnAddress2Config) refers to stm32f10x_i2c.o(.text.I2C_OwnAddress2Config) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_DualAddressCmd) refers to stm32f10x_i2c.o(.text.I2C_DualAddressCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GeneralCallCmd) refers to stm32f10x_i2c.o(.text.I2C_GeneralCallCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ITConfig) refers to stm32f10x_i2c.o(.text.I2C_ITConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_SendData) refers to stm32f10x_i2c.o(.text.I2C_SendData) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ReceiveData) refers to stm32f10x_i2c.o(.text.I2C_ReceiveData) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_Send7bitAddress) refers to stm32f10x_i2c.o(.text.I2C_Send7bitAddress) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ReadRegister) refers to stm32f10x_i2c.o(.text.I2C_ReadRegister) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_SoftwareResetCmd) refers to stm32f10x_i2c.o(.text.I2C_SoftwareResetCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_NACKPositionConfig) refers to stm32f10x_i2c.o(.text.I2C_NACKPositionConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_SMBusAlertConfig) refers to stm32f10x_i2c.o(.text.I2C_SMBusAlertConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_TransmitPEC) refers to stm32f10x_i2c.o(.text.I2C_TransmitPEC) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_PECPositionConfig) refers to stm32f10x_i2c.o(.text.I2C_PECPositionConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_CalculatePEC) refers to stm32f10x_i2c.o(.text.I2C_CalculatePEC) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetPEC) refers to stm32f10x_i2c.o(.text.I2C_GetPEC) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ARPCmd) refers to stm32f10x_i2c.o(.text.I2C_ARPCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_StretchClockCmd) refers to stm32f10x_i2c.o(.text.I2C_StretchClockCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_FastModeDutyCycleConfig) refers to stm32f10x_i2c.o(.text.I2C_FastModeDutyCycleConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_CheckEvent) refers to stm32f10x_i2c.o(.text.I2C_CheckEvent) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetLastEvent) refers to stm32f10x_i2c.o(.text.I2C_GetLastEvent) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetFlagStatus) refers to stm32f10x_i2c.o(.text.I2C_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ClearFlag) refers to stm32f10x_i2c.o(.text.I2C_ClearFlag) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetITStatus) refers to stm32f10x_i2c.o(.text.I2C_GetITStatus) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ClearITPendingBit) refers to stm32f10x_i2c.o(.text.I2C_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_WriteAccessCmd) refers to stm32f10x_iwdg.o(.text.IWDG_WriteAccessCmd) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_SetPrescaler) refers to stm32f10x_iwdg.o(.text.IWDG_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_SetReload) refers to stm32f10x_iwdg.o(.text.IWDG_SetReload) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_ReloadCounter) refers to stm32f10x_iwdg.o(.text.IWDG_ReloadCounter) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_Enable) refers to stm32f10x_iwdg.o(.text.IWDG_Enable) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_GetFlagStatus) refers to stm32f10x_iwdg.o(.text.IWDG_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_pwr.o(.text.PWR_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_DeInit) refers to stm32f10x_pwr.o(.text.PWR_DeInit) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_BackupAccessCmd) refers to stm32f10x_pwr.o(.text.PWR_BackupAccessCmd) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_PVDCmd) refers to stm32f10x_pwr.o(.text.PWR_PVDCmd) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_PVDLevelConfig) refers to stm32f10x_pwr.o(.text.PWR_PVDLevelConfig) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_WakeUpPinCmd) refers to stm32f10x_pwr.o(.text.PWR_WakeUpPinCmd) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_EnterSTOPMode) refers to stm32f10x_pwr.o(.text.PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_EnterSTANDBYMode) refers to stm32f10x_pwr.o(.text.PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_GetFlagStatus) refers to stm32f10x_pwr.o(.text.PWR_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_ClearFlag) refers to stm32f10x_pwr.o(.text.PWR_ClearFlag) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_DeInit) refers to stm32f10x_rcc.o(.text.RCC_DeInit) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_HSEConfig) refers to stm32f10x_rcc.o(.text.RCC_HSEConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(.text.RCC_WaitForHSEStartUp) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetFlagStatus) refers to stm32f10x_rcc.o(.text.RCC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_AdjustHSICalibrationValue) refers to stm32f10x_rcc.o(.text.RCC_AdjustHSICalibrationValue) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_HSICmd) refers to stm32f10x_rcc.o(.text.RCC_HSICmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_PLLConfig) refers to stm32f10x_rcc.o(.text.RCC_PLLConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_PLLCmd) refers to stm32f10x_rcc.o(.text.RCC_PLLCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_SYSCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_SYSCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetSYSCLKSource) refers to stm32f10x_rcc.o(.text.RCC_GetSYSCLKSource) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_HCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_HCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_PCLK1Config) refers to stm32f10x_rcc.o(.text.RCC_PCLK1Config) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_PCLK2Config) refers to stm32f10x_rcc.o(.text.RCC_PCLK2Config) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ITConfig) refers to stm32f10x_rcc.o(.text.RCC_ITConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_USBCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_USBCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ADCCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_ADCCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_LSEConfig) refers to stm32f10x_rcc.o(.text.RCC_LSEConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_LSICmd) refers to stm32f10x_rcc.o(.text.RCC_LSICmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_RTCCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_RTCCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_RTCCLKCmd) refers to stm32f10x_rcc.o(.text.RCC_RTCCLKCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.text.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.rodata.APBAHBPrescTable) for APBAHBPrescTable
    stm32f10x_rcc.o(.text.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.rodata.ADCPrescTable) for ADCPrescTable
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.text.RCC_GetClocksFreq) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_AHBPeriphClockCmd) refers to stm32f10x_rcc.o(.text.RCC_AHBPeriphClockCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB2PeriphClockCmd) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB1PeriphClockCmd) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB2PeriphResetCmd) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB1PeriphResetCmd) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_BackupResetCmd) refers to stm32f10x_rcc.o(.text.RCC_BackupResetCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClockSecuritySystemCmd) refers to stm32f10x_rcc.o(.text.RCC_ClockSecuritySystemCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_MCOConfig) refers to stm32f10x_rcc.o(.text.RCC_MCOConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClearFlag) refers to stm32f10x_rcc.o(.text.RCC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetITStatus) refers to stm32f10x_rcc.o(.text.RCC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClearITPendingBit) refers to stm32f10x_rcc.o(.text.RCC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_ITConfig) refers to stm32f10x_rtc.o(.text.RTC_ITConfig) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_EnterConfigMode) refers to stm32f10x_rtc.o(.text.RTC_EnterConfigMode) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_ExitConfigMode) refers to stm32f10x_rtc.o(.text.RTC_ExitConfigMode) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetCounter) refers to stm32f10x_rtc.o(.text.RTC_GetCounter) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetCounter) refers to stm32f10x_rtc.o(.text.RTC_SetCounter) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetPrescaler) refers to stm32f10x_rtc.o(.text.RTC_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetAlarm) refers to stm32f10x_rtc.o(.text.RTC_SetAlarm) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetDivider) refers to stm32f10x_rtc.o(.text.RTC_GetDivider) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_WaitForLastTask) refers to stm32f10x_rtc.o(.text.RTC_WaitForLastTask) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_WaitForSynchro) refers to stm32f10x_rtc.o(.text.RTC_WaitForSynchro) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetFlagStatus) refers to stm32f10x_rtc.o(.text.RTC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_ClearFlag) refers to stm32f10x_rtc.o(.text.RTC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetITStatus) refers to stm32f10x_rtc.o(.text.RTC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_ClearITPendingBit) refers to stm32f10x_rtc.o(.text.RTC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DeInit) refers to stm32f10x_sdio.o(.text.SDIO_DeInit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_Init) refers to stm32f10x_sdio.o(.text.SDIO_Init) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StructInit) refers to stm32f10x_sdio.o(.text.SDIO_StructInit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClockCmd) refers to stm32f10x_sdio.o(.text.SDIO_ClockCmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetPowerState) refers to stm32f10x_sdio.o(.text.SDIO_SetPowerState) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetPowerState) refers to stm32f10x_sdio.o(.text.SDIO_GetPowerState) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ITConfig) refers to stm32f10x_sdio.o(.text.SDIO_ITConfig) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DMACmd) refers to stm32f10x_sdio.o(.text.SDIO_DMACmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendCommand) refers to stm32f10x_sdio.o(.text.SDIO_SendCommand) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CmdStructInit) refers to stm32f10x_sdio.o(.text.SDIO_CmdStructInit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetCommandResponse) refers to stm32f10x_sdio.o(.text.SDIO_GetCommandResponse) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetResponse) refers to stm32f10x_sdio.o(.text.SDIO_GetResponse) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DataConfig) refers to stm32f10x_sdio.o(.text.SDIO_DataConfig) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DataStructInit) refers to stm32f10x_sdio.o(.text.SDIO_DataStructInit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetDataCounter) refers to stm32f10x_sdio.o(.text.SDIO_GetDataCounter) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ReadData) refers to stm32f10x_sdio.o(.text.SDIO_ReadData) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_WriteData) refers to stm32f10x_sdio.o(.text.SDIO_WriteData) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetFIFOCount) refers to stm32f10x_sdio.o(.text.SDIO_GetFIFOCount) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StartSDIOReadWait) refers to stm32f10x_sdio.o(.text.SDIO_StartSDIOReadWait) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StopSDIOReadWait) refers to stm32f10x_sdio.o(.text.SDIO_StopSDIOReadWait) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetSDIOReadWaitMode) refers to stm32f10x_sdio.o(.text.SDIO_SetSDIOReadWaitMode) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetSDIOOperation) refers to stm32f10x_sdio.o(.text.SDIO_SetSDIOOperation) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendSDIOSuspendCmd) refers to stm32f10x_sdio.o(.text.SDIO_SendSDIOSuspendCmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CommandCompletionCmd) refers to stm32f10x_sdio.o(.text.SDIO_CommandCompletionCmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CEATAITCmd) refers to stm32f10x_sdio.o(.text.SDIO_CEATAITCmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendCEATACmd) refers to stm32f10x_sdio.o(.text.SDIO_SendCEATACmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetFlagStatus) refers to stm32f10x_sdio.o(.text.SDIO_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClearFlag) refers to stm32f10x_sdio.o(.text.SDIO_ClearFlag) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetITStatus) refers to stm32f10x_sdio.o(.text.SDIO_GetITStatus) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClearITPendingBit) refers to stm32f10x_sdio.o(.text.SDIO_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_spi.o(.text.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(.text.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_DeInit) refers to stm32f10x_spi.o(.text.SPI_I2S_DeInit) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_Init) refers to stm32f10x_spi.o(.text.SPI_Init) for [Anonymous Symbol]
    stm32f10x_spi.o(.text.I2S_Init) refers to stm32f10x_rcc.o(.text.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(.ARM.exidx.text.I2S_Init) refers to stm32f10x_spi.o(.text.I2S_Init) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_StructInit) refers to stm32f10x_spi.o(.text.SPI_StructInit) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.I2S_StructInit) refers to stm32f10x_spi.o(.text.I2S_StructInit) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_Cmd) refers to stm32f10x_spi.o(.text.SPI_Cmd) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.I2S_Cmd) refers to stm32f10x_spi.o(.text.I2S_Cmd) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ITConfig) refers to stm32f10x_spi.o(.text.SPI_I2S_ITConfig) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_DMACmd) refers to stm32f10x_spi.o(.text.SPI_I2S_DMACmd) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_SendData) refers to stm32f10x_spi.o(.text.SPI_I2S_SendData) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ReceiveData) refers to stm32f10x_spi.o(.text.SPI_I2S_ReceiveData) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_NSSInternalSoftwareConfig) refers to stm32f10x_spi.o(.text.SPI_NSSInternalSoftwareConfig) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_SSOutputCmd) refers to stm32f10x_spi.o(.text.SPI_SSOutputCmd) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_DataSizeConfig) refers to stm32f10x_spi.o(.text.SPI_DataSizeConfig) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_TransmitCRC) refers to stm32f10x_spi.o(.text.SPI_TransmitCRC) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_CalculateCRC) refers to stm32f10x_spi.o(.text.SPI_CalculateCRC) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_GetCRC) refers to stm32f10x_spi.o(.text.SPI_GetCRC) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_GetCRCPolynomial) refers to stm32f10x_spi.o(.text.SPI_GetCRCPolynomial) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_BiDirectionalLineConfig) refers to stm32f10x_spi.o(.text.SPI_BiDirectionalLineConfig) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_GetFlagStatus) refers to stm32f10x_spi.o(.text.SPI_I2S_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ClearFlag) refers to stm32f10x_spi.o(.text.SPI_I2S_ClearFlag) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_GetITStatus) refers to stm32f10x_spi.o(.text.SPI_I2S_GetITStatus) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ClearITPendingBit) refers to stm32f10x_spi.o(.text.SPI_I2S_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_tim.o(.text.TIM_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(.text.TIM_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(.ARM.exidx.text.TIM_DeInit) refers to stm32f10x_tim.o(.text.TIM_DeInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_TimeBaseInit) refers to stm32f10x_tim.o(.text.TIM_TimeBaseInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1Init) refers to stm32f10x_tim.o(.text.TIM_OC1Init) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2Init) refers to stm32f10x_tim.o(.text.TIM_OC2Init) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3Init) refers to stm32f10x_tim.o(.text.TIM_OC3Init) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4Init) refers to stm32f10x_tim.o(.text.TIM_OC4Init) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ICInit) refers to stm32f10x_tim.o(.text.TIM_ICInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC1Prescaler) refers to stm32f10x_tim.o(.text.TIM_SetIC1Prescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC2Prescaler) refers to stm32f10x_tim.o(.text.TIM_SetIC2Prescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC3Prescaler) refers to stm32f10x_tim.o(.text.TIM_SetIC3Prescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC4Prescaler) refers to stm32f10x_tim.o(.text.TIM_SetIC4Prescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_PWMIConfig) refers to stm32f10x_tim.o(.text.TIM_PWMIConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_BDTRConfig) refers to stm32f10x_tim.o(.text.TIM_BDTRConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_TimeBaseStructInit) refers to stm32f10x_tim.o(.text.TIM_TimeBaseStructInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OCStructInit) refers to stm32f10x_tim.o(.text.TIM_OCStructInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ICStructInit) refers to stm32f10x_tim.o(.text.TIM_ICStructInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_BDTRStructInit) refers to stm32f10x_tim.o(.text.TIM_BDTRStructInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_Cmd) refers to stm32f10x_tim.o(.text.TIM_Cmd) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CtrlPWMOutputs) refers to stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ITConfig) refers to stm32f10x_tim.o(.text.TIM_ITConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GenerateEvent) refers to stm32f10x_tim.o(.text.TIM_GenerateEvent) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_DMAConfig) refers to stm32f10x_tim.o(.text.TIM_DMAConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_DMACmd) refers to stm32f10x_tim.o(.text.TIM_DMACmd) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_InternalClockConfig) refers to stm32f10x_tim.o(.text.TIM_InternalClockConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(.text.TIM_ITRxExternalClockConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectInputTrigger) refers to stm32f10x_tim.o(.text.TIM_SelectInputTrigger) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(.text.TIM_TIxExternalClockConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(.text.TIM_ETRClockMode1Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRConfig) refers to stm32f10x_tim.o(.text.TIM_ETRConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(.text.TIM_ETRClockMode2Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_PrescalerConfig) refers to stm32f10x_tim.o(.text.TIM_PrescalerConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CounterModeConfig) refers to stm32f10x_tim.o(.text.TIM_CounterModeConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_EncoderInterfaceConfig) refers to stm32f10x_tim.o(.text.TIM_EncoderInterfaceConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC1Config) refers to stm32f10x_tim.o(.text.TIM_ForcedOC1Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC2Config) refers to stm32f10x_tim.o(.text.TIM_ForcedOC2Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC3Config) refers to stm32f10x_tim.o(.text.TIM_ForcedOC3Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC4Config) refers to stm32f10x_tim.o(.text.TIM_ForcedOC4Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ARRPreloadConfig) refers to stm32f10x_tim.o(.text.TIM_ARRPreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectCOM) refers to stm32f10x_tim.o(.text.TIM_SelectCOM) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectCCDMA) refers to stm32f10x_tim.o(.text.TIM_SelectCCDMA) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CCPreloadControl) refers to stm32f10x_tim.o(.text.TIM_CCPreloadControl) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1PreloadConfig) refers to stm32f10x_tim.o(.text.TIM_OC1PreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2PreloadConfig) refers to stm32f10x_tim.o(.text.TIM_OC2PreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3PreloadConfig) refers to stm32f10x_tim.o(.text.TIM_OC3PreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4PreloadConfig) refers to stm32f10x_tim.o(.text.TIM_OC4PreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1FastConfig) refers to stm32f10x_tim.o(.text.TIM_OC1FastConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2FastConfig) refers to stm32f10x_tim.o(.text.TIM_OC2FastConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3FastConfig) refers to stm32f10x_tim.o(.text.TIM_OC3FastConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4FastConfig) refers to stm32f10x_tim.o(.text.TIM_OC4FastConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC1Ref) refers to stm32f10x_tim.o(.text.TIM_ClearOC1Ref) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC2Ref) refers to stm32f10x_tim.o(.text.TIM_ClearOC2Ref) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC3Ref) refers to stm32f10x_tim.o(.text.TIM_ClearOC3Ref) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC4Ref) refers to stm32f10x_tim.o(.text.TIM_ClearOC4Ref) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1PolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC1PolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1NPolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC1NPolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2PolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC2PolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2NPolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC2NPolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3PolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC3PolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3NPolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC3NPolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4PolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC4PolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CCxCmd) refers to stm32f10x_tim.o(.text.TIM_CCxCmd) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CCxNCmd) refers to stm32f10x_tim.o(.text.TIM_CCxNCmd) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOCxM) refers to stm32f10x_tim.o(.text.TIM_SelectOCxM) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_UpdateDisableConfig) refers to stm32f10x_tim.o(.text.TIM_UpdateDisableConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_UpdateRequestConfig) refers to stm32f10x_tim.o(.text.TIM_UpdateRequestConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectHallSensor) refers to stm32f10x_tim.o(.text.TIM_SelectHallSensor) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOnePulseMode) refers to stm32f10x_tim.o(.text.TIM_SelectOnePulseMode) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOutputTrigger) refers to stm32f10x_tim.o(.text.TIM_SelectOutputTrigger) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectSlaveMode) refers to stm32f10x_tim.o(.text.TIM_SelectSlaveMode) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectMasterSlaveMode) refers to stm32f10x_tim.o(.text.TIM_SelectMasterSlaveMode) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCounter) refers to stm32f10x_tim.o(.text.TIM_SetCounter) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetAutoreload) refers to stm32f10x_tim.o(.text.TIM_SetAutoreload) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare1) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare2) refers to stm32f10x_tim.o(.text.TIM_SetCompare2) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare3) refers to stm32f10x_tim.o(.text.TIM_SetCompare3) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare4) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetClockDivision) refers to stm32f10x_tim.o(.text.TIM_SetClockDivision) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture1) refers to stm32f10x_tim.o(.text.TIM_GetCapture1) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture2) refers to stm32f10x_tim.o(.text.TIM_GetCapture2) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture3) refers to stm32f10x_tim.o(.text.TIM_GetCapture3) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture4) refers to stm32f10x_tim.o(.text.TIM_GetCapture4) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCounter) refers to stm32f10x_tim.o(.text.TIM_GetCounter) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetPrescaler) refers to stm32f10x_tim.o(.text.TIM_GetPrescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetFlagStatus) refers to stm32f10x_tim.o(.text.TIM_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearFlag) refers to stm32f10x_tim.o(.text.TIM_ClearFlag) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetITStatus) refers to stm32f10x_tim.o(.text.TIM_GetITStatus) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearITPendingBit) refers to stm32f10x_tim.o(.text.TIM_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_usart.o(.text.USART_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(.text.USART_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(.ARM.exidx.text.USART_DeInit) refers to stm32f10x_usart.o(.text.USART_DeInit) for [Anonymous Symbol]
    stm32f10x_usart.o(.text.USART_Init) refers to stm32f10x_rcc.o(.text.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_usart.o(.ARM.exidx.text.USART_Init) refers to stm32f10x_usart.o(.text.USART_Init) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_StructInit) refers to stm32f10x_usart.o(.text.USART_StructInit) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ClockInit) refers to stm32f10x_usart.o(.text.USART_ClockInit) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ClockStructInit) refers to stm32f10x_usart.o(.text.USART_ClockStructInit) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_Cmd) refers to stm32f10x_usart.o(.text.USART_Cmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ITConfig) refers to stm32f10x_usart.o(.text.USART_ITConfig) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_DMACmd) refers to stm32f10x_usart.o(.text.USART_DMACmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SetAddress) refers to stm32f10x_usart.o(.text.USART_SetAddress) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_WakeUpConfig) refers to stm32f10x_usart.o(.text.USART_WakeUpConfig) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ReceiverWakeUpCmd) refers to stm32f10x_usart.o(.text.USART_ReceiverWakeUpCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_LINBreakDetectLengthConfig) refers to stm32f10x_usart.o(.text.USART_LINBreakDetectLengthConfig) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_LINCmd) refers to stm32f10x_usart.o(.text.USART_LINCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SendData) refers to stm32f10x_usart.o(.text.USART_SendData) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ReceiveData) refers to stm32f10x_usart.o(.text.USART_ReceiveData) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SendBreak) refers to stm32f10x_usart.o(.text.USART_SendBreak) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SetGuardTime) refers to stm32f10x_usart.o(.text.USART_SetGuardTime) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SetPrescaler) refers to stm32f10x_usart.o(.text.USART_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SmartCardCmd) refers to stm32f10x_usart.o(.text.USART_SmartCardCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SmartCardNACKCmd) refers to stm32f10x_usart.o(.text.USART_SmartCardNACKCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_HalfDuplexCmd) refers to stm32f10x_usart.o(.text.USART_HalfDuplexCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_OverSampling8Cmd) refers to stm32f10x_usart.o(.text.USART_OverSampling8Cmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_OneBitMethodCmd) refers to stm32f10x_usart.o(.text.USART_OneBitMethodCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_IrDAConfig) refers to stm32f10x_usart.o(.text.USART_IrDAConfig) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_IrDACmd) refers to stm32f10x_usart.o(.text.USART_IrDACmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_GetFlagStatus) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ClearFlag) refers to stm32f10x_usart.o(.text.USART_ClearFlag) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_GetITStatus) refers to stm32f10x_usart.o(.text.USART_GetITStatus) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ClearITPendingBit) refers to stm32f10x_usart.o(.text.USART_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.text.WWDG_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_DeInit) refers to stm32f10x_wwdg.o(.text.WWDG_DeInit) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetPrescaler) refers to stm32f10x_wwdg.o(.text.WWDG_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetWindowValue) refers to stm32f10x_wwdg.o(.text.WWDG_SetWindowValue) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_EnableIT) refers to stm32f10x_wwdg.o(.text.WWDG_EnableIT) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetCounter) refers to stm32f10x_wwdg.o(.text.WWDG_SetCounter) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_Enable) refers to stm32f10x_wwdg.o(.text.WWDG_Enable) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_GetFlagStatus) refers to stm32f10x_wwdg.o(.text.WWDG_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_ClearFlag) refers to stm32f10x_wwdg.o(.text.WWDG_ClearFlag) for [Anonymous Symbol]
    system_stm32f10x.o(.ARM.exidx.text.SystemInit) refers to system_stm32f10x.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f10x.o(.text.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f10x.o(.text.SystemCoreClockUpdate) refers to system_stm32f10x.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f10x.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f10x.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_PSP) refers to core_cm3.o(.text.__get_PSP) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_PSP) refers to core_cm3.o(.text.__set_PSP) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_MSP) refers to core_cm3.o(.text.__get_MSP) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_MSP) refers to core_cm3.o(.text.__set_MSP) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_BASEPRI) refers to core_cm3.o(.text.__get_BASEPRI) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_BASEPRI) refers to core_cm3.o(.text.__set_BASEPRI) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_PRIMASK) refers to core_cm3.o(.text.__get_PRIMASK) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_PRIMASK) refers to core_cm3.o(.text.__set_PRIMASK) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_FAULTMASK) refers to core_cm3.o(.text.__get_FAULTMASK) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_FAULTMASK) refers to core_cm3.o(.text.__set_FAULTMASK) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_CONTROL) refers to core_cm3.o(.text.__get_CONTROL) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_CONTROL) refers to core_cm3.o(.text.__set_CONTROL) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__REV) refers to core_cm3.o(.text.__REV) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__REV16) refers to core_cm3.o(.text.__REV16) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__REVSH) refers to core_cm3.o(.text.__REVSH) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__RBIT) refers to core_cm3.o(.text.__RBIT) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__LDREXB) refers to core_cm3.o(.text.__LDREXB) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__LDREXH) refers to core_cm3.o(.text.__LDREXH) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__LDREXW) refers to core_cm3.o(.text.__LDREXW) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__STREXB) refers to core_cm3.o(.text.__STREXB) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__STREXH) refers to core_cm3.o(.text.__STREXH) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__STREXW) refers to core_cm3.o(.text.__STREXW) for [Anonymous Symbol]
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to encoder.o(.text.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to encoder.o(.text.EXTI1_IRQHandler) for EXTI1_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to encoder.o(.text.EXTI2_IRQHandler) for EXTI2_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to usart1.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to encoder.o(.text.TIM6_IRQHandler) for TIM6_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to timer7.o(.text.TIM7_IRQHandler) for TIM7_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    main.o(.ARM.exidx.text.Delay_ms) refers to main.o(.text.Delay_ms) for [Anonymous Symbol]
    main.o(.text.main) refers to system_stm32f10x.o(.text.SystemInit) for SystemInit
    main.o(.text.main) refers to gimbal.o(.text.Gimbal_Init) for Gimbal_Init
    main.o(.text.main) refers to usart1.o(.text.USART1_Init) for USART1_Init
    main.o(.text.main) refers to gimbal.o(.text.Gimbal_AdjustToCenterTarget) for Gimbal_AdjustToCenterTarget
    main.o(.text.main) refers to gimbal.o(.text.Gimbal_UpdatePosition) for Gimbal_UpdatePosition
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f10x_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f10x_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f10x_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f10x_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f10x_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f10x_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f10x_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f10x_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f10x_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.text.USART1_IRQHandler) refers to stm32f10x_usart.o(.text.USART_GetITStatus) for USART_GetITStatus
    stm32f10x_it.o(.text.USART1_IRQHandler) refers to stm32f10x_usart.o(.text.USART_ReceiveData) for USART_ReceiveData
    stm32f10x_it.o(.text.USART1_IRQHandler) refers to usart1.o(.text.USART1_ProcessReceivedData) for USART1_ProcessReceivedData
    stm32f10x_it.o(.text.USART1_IRQHandler) refers to stm32f10x_usart.o(.text.USART_ClearITPendingBit) for USART_ClearITPendingBit
    stm32f10x_it.o(.ARM.exidx.text.USART1_IRQHandler) refers to stm32f10x_it.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    pwm1.o(.text.PWM_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pwm1.o(.text.PWM_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    pwm1.o(.text.PWM_Init) refers to system_stm32f10x.o(.data.SystemCoreClock) for SystemCoreClock
    pwm1.o(.text.PWM_Init) refers to pwm1.o(.bss.pwmPeriod) for pwmPeriod
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_TimeBaseInit) for TIM_TimeBaseInit
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC3Init) for TIM_OC3Init
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC1Init) for TIM_OC1Init
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC4Init) for TIM_OC4Init
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC2Init) for TIM_OC2Init
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    pwm1.o(.ARM.exidx.text.PWM_Init) refers to pwm1.o(.text.PWM_Init) for [Anonymous Symbol]
    pwm1.o(.text.PWM_SetDutyCycle) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmplt
    pwm1.o(.text.PWM_SetDutyCycle) refers to pwm1.o(.bss.pwmPeriod) for pwmPeriod
    pwm1.o(.text.PWM_SetDutyCycle) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    pwm1.o(.text.PWM_SetDutyCycle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pwm1.o(.text.PWM_SetDutyCycle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    pwm1.o(.text.PWM_SetDutyCycle) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    pwm1.o(.text.PWM_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for TIM_SetCompare1
    pwm1.o(.text.PWM_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare3) for TIM_SetCompare3
    pwm1.o(.text.PWM_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for TIM_SetCompare4
    pwm1.o(.text.PWM_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare2) for TIM_SetCompare2
    pwm1.o(.ARM.exidx.text.PWM_SetDutyCycle) refers to pwm1.o(.text.PWM_SetDutyCycle) for [Anonymous Symbol]
    pwm1.o(.text.PWM_Start) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm1.o(.text.PWM_Start) refers to stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    pwm1.o(.ARM.exidx.text.PWM_Start) refers to pwm1.o(.text.PWM_Start) for [Anonymous Symbol]
    pwm1.o(.text.PWM_Stop) refers to stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    pwm1.o(.text.PWM_Stop) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm1.o(.ARM.exidx.text.PWM_Stop) refers to pwm1.o(.text.PWM_Stop) for [Anonymous Symbol]
    pwm1.o(.text.PWM_SetDeadTime) refers to stm32f10x_tim.o(.text.TIM_BDTRStructInit) for TIM_BDTRStructInit
    pwm1.o(.text.PWM_SetDeadTime) refers to stm32f10x_tim.o(.text.TIM_BDTRConfig) for TIM_BDTRConfig
    pwm1.o(.ARM.exidx.text.PWM_SetDeadTime) refers to pwm1.o(.text.PWM_SetDeadTime) for [Anonymous Symbol]
    pwm1.o(.text.PwmConfig) refers to pwm1.o(.text.PWM_Init) for PWM_Init
    pwm1.o(.text.PwmConfig) refers to pwm1.o(.bss.pwmPeriod) for pwmPeriod
    pwm1.o(.text.PwmConfig) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    pwm1.o(.text.PwmConfig) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pwm1.o(.text.PwmConfig) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    pwm1.o(.text.PwmConfig) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    pwm1.o(.text.PwmConfig) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for TIM_SetCompare1
    pwm1.o(.text.PwmConfig) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm1.o(.text.PwmConfig) refers to stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    pwm1.o(.ARM.exidx.text.PwmConfig) refers to pwm1.o(.text.PwmConfig) for [Anonymous Symbol]
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    pwm2.o(.text.PWM2_Init) refers to system_stm32f10x.o(.data.SystemCoreClock) for SystemCoreClock
    pwm2.o(.text.PWM2_Init) refers to pwm2.o(.bss.pwmPeriod) for pwmPeriod
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_TimeBaseInit) for TIM_TimeBaseInit
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_OC3Init) for TIM_OC3Init
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_OC1Init) for TIM_OC1Init
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_OC4Init) for TIM_OC4Init
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_OC2Init) for TIM_OC2Init
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm2.o(.ARM.exidx.text.PWM2_Init) refers to pwm2.o(.text.PWM2_Init) for [Anonymous Symbol]
    pwm2.o(.text.PWM2_SetDutyCycle) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmplt
    pwm2.o(.text.PWM2_SetDutyCycle) refers to pwm2.o(.bss.pwmPeriod) for pwmPeriod
    pwm2.o(.text.PWM2_SetDutyCycle) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    pwm2.o(.text.PWM2_SetDutyCycle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pwm2.o(.text.PWM2_SetDutyCycle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    pwm2.o(.text.PWM2_SetDutyCycle) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    pwm2.o(.text.PWM2_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for TIM_SetCompare1
    pwm2.o(.text.PWM2_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare3) for TIM_SetCompare3
    pwm2.o(.text.PWM2_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for TIM_SetCompare4
    pwm2.o(.text.PWM2_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare2) for TIM_SetCompare2
    pwm2.o(.ARM.exidx.text.PWM2_SetDutyCycle) refers to pwm2.o(.text.PWM2_SetDutyCycle) for [Anonymous Symbol]
    pwm2.o(.text.PWM2_Start) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm2.o(.ARM.exidx.text.PWM2_Start) refers to pwm2.o(.text.PWM2_Start) for [Anonymous Symbol]
    pwm2.o(.text.PWM2_Stop) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm2.o(.ARM.exidx.text.PWM2_Stop) refers to pwm2.o(.text.PWM2_Stop) for [Anonymous Symbol]
    pwm2.o(.text.Pwm2Config_Channel4) refers to pwm2.o(.text.PWM2_Init) for PWM2_Init
    pwm2.o(.text.Pwm2Config_Channel4) refers to pwm2.o(.bss.pwmPeriod) for pwmPeriod
    pwm2.o(.text.Pwm2Config_Channel4) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    pwm2.o(.text.Pwm2Config_Channel4) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pwm2.o(.text.Pwm2Config_Channel4) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    pwm2.o(.text.Pwm2Config_Channel4) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    pwm2.o(.text.Pwm2Config_Channel4) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for TIM_SetCompare4
    pwm2.o(.text.Pwm2Config_Channel4) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm2.o(.ARM.exidx.text.Pwm2Config_Channel4) refers to pwm2.o(.text.Pwm2Config_Channel4) for [Anonymous Symbol]
    usart1.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.text) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.text.parse_received_data) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.text.parse_received_data) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.text.parse_received_data) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.text.parse_received_data) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.text.parse_received_data) refers to usart1.o(.data.redx) for redx
    usart1.o(.text.parse_received_data) refers to usart1.o(.data.redy) for redy
    usart1.o(.text.parse_received_data) refers to usart1.o(.rodata.str1.1) for .L.str
    usart1.o(.text.parse_received_data) refers to __0sscanf.o(.text) for __0sscanf
    usart1.o(.text.parse_received_data) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for USART_GetFlagStatus
    usart1.o(.text.parse_received_data) refers to stm32f10x_usart.o(.text.USART_SendData) for USART_SendData
    usart1.o(.text.parse_received_data) refers to __2sprintf.o(.text) for __2sprintf
    usart1.o(.text.parse_received_data) refers to usart1.o(.bss.vertex_x) for vertex_x
    usart1.o(.text.parse_received_data) refers to usart1.o(.bss.vertex_y) for vertex_y
    usart1.o(.text.parse_received_data) refers to usart1.o(.bss.centerx) for centerx
    usart1.o(.text.parse_received_data) refers to usart1.o(.bss.centery) for centery
    usart1.o(.ARM.exidx.text.parse_received_data) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.ARM.exidx.text.parse_received_data) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.ARM.exidx.text.parse_received_data) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.ARM.exidx.text.parse_received_data) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.ARM.exidx.text.parse_received_data) refers to usart1.o(.text.parse_received_data) for [Anonymous Symbol]
    usart1.o(.text.USART1_SendString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.text.USART1_SendString) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.text.USART1_SendString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.text.USART1_SendString) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.text.USART1_SendString) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for USART_GetFlagStatus
    usart1.o(.text.USART1_SendString) refers to stm32f10x_usart.o(.text.USART_SendData) for USART_SendData
    usart1.o(.ARM.exidx.text.USART1_SendString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.ARM.exidx.text.USART1_SendString) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.ARM.exidx.text.USART1_SendString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.ARM.exidx.text.USART1_SendString) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.ARM.exidx.text.USART1_SendString) refers to usart1.o(.text.USART1_SendString) for [Anonymous Symbol]
    usart1.o(.text.USART1_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.text.USART1_Init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.text.USART1_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.text.USART1_Init) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.text.USART1_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart1.o(.text.USART1_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    usart1.o(.text.USART1_Init) refers to stm32f10x_usart.o(.text.USART_Init) for USART_Init
    usart1.o(.text.USART1_Init) refers to stm32f10x_usart.o(.text.USART_ITConfig) for USART_ITConfig
    usart1.o(.text.USART1_Init) refers to misc.o(.text.NVIC_Init) for NVIC_Init
    usart1.o(.text.USART1_Init) refers to stm32f10x_usart.o(.text.USART_Cmd) for USART_Cmd
    usart1.o(.ARM.exidx.text.USART1_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.ARM.exidx.text.USART1_Init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.ARM.exidx.text.USART1_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.ARM.exidx.text.USART1_Init) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.ARM.exidx.text.USART1_Init) refers to usart1.o(.text.USART1_Init) for [Anonymous Symbol]
    usart1.o(.text.USART1_SendChar) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.text.USART1_SendChar) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.text.USART1_SendChar) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.text.USART1_SendChar) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.text.USART1_SendChar) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for USART_GetFlagStatus
    usart1.o(.text.USART1_SendChar) refers to stm32f10x_usart.o(.text.USART_SendData) for USART_SendData
    usart1.o(.ARM.exidx.text.USART1_SendChar) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.ARM.exidx.text.USART1_SendChar) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.ARM.exidx.text.USART1_SendChar) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.ARM.exidx.text.USART1_SendChar) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.ARM.exidx.text.USART1_SendChar) refers to usart1.o(.text.USART1_SendChar) for [Anonymous Symbol]
    usart1.o(.text.USART1_IRQHandler) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.text.USART1_IRQHandler) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.text.USART1_IRQHandler) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.text.USART1_IRQHandler) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.text.USART1_IRQHandler) refers to stm32f10x_usart.o(.text.USART_GetITStatus) for USART_GetITStatus
    usart1.o(.text.USART1_IRQHandler) refers to stm32f10x_usart.o(.text.USART_ReceiveData) for USART_ReceiveData
    usart1.o(.text.USART1_IRQHandler) refers to usart1.o(.bss.rx_index) for rx_index
    usart1.o(.text.USART1_IRQHandler) refers to usart1.o(.bss.rx_buffer) for rx_buffer
    usart1.o(.text.USART1_IRQHandler) refers to usart1.o(.text.parse_received_data) for parse_received_data
    usart1.o(.text.USART1_IRQHandler) refers to stm32f10x_usart.o(.text.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart1.o(.ARM.exidx.text.USART1_IRQHandler) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.ARM.exidx.text.USART1_IRQHandler) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.ARM.exidx.text.USART1_IRQHandler) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.ARM.exidx.text.USART1_IRQHandler) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.ARM.exidx.text.USART1_IRQHandler) refers to usart1.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    usart1.o(.text.USART1_ProcessReceivedData) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.text.USART1_ProcessReceivedData) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.text.USART1_ProcessReceivedData) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.text.USART1_ProcessReceivedData) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.text.USART1_ProcessReceivedData) refers to usart1.o(.bss.rx_index) for rx_index
    usart1.o(.text.USART1_ProcessReceivedData) refers to usart1.o(.bss.rx_buffer) for rx_buffer
    usart1.o(.text.USART1_ProcessReceivedData) refers to usart1.o(.text.parse_received_data) for parse_received_data
    usart1.o(.ARM.exidx.text.USART1_ProcessReceivedData) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.ARM.exidx.text.USART1_ProcessReceivedData) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.ARM.exidx.text.USART1_ProcessReceivedData) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.ARM.exidx.text.USART1_ProcessReceivedData) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.ARM.exidx.text.USART1_ProcessReceivedData) refers to usart1.o(.text.USART1_ProcessReceivedData) for [Anonymous Symbol]
    usart1.o(.data.redx) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.data.redx) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.data.redx) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.data.redx) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.data.redy) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.data.redy) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.data.redy) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.data.redy) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.bss.centerx) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.bss.centerx) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.bss.centerx) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.bss.centerx) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.bss.centery) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.bss.centery) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.bss.centery) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.bss.centery) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.bss.vertex_x) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.bss.vertex_x) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.bss.vertex_x) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.bss.vertex_x) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.bss.vertex_y) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.bss.vertex_y) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.bss.vertex_y) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.bss.vertex_y) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.rodata.str1.1) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.rodata.str1.1) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.rodata.str1.1) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.rodata.str1.1) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.bss.rx_index) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.bss.rx_index) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.bss.rx_index) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.bss.rx_index) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.bss.rx_buffer) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.bss.rx_buffer) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.bss.rx_buffer) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.bss.rx_buffer) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_vision.o(.text.UART_Vision_Init) refers to uart_vision.o(.bss.vision_display_buffer) for vision_display_buffer
    uart_vision.o(.text.UART_Vision_Init) refers to uart_vision.o(.bss.rx_buffer) for rx_buffer
    uart_vision.o(.text.UART_Vision_Init) refers to uart_vision.o(.bss.rx_index) for rx_index
    uart_vision.o(.ARM.exidx.text.UART_Vision_Init) refers to uart_vision.o(.text.UART_Vision_Init) for [Anonymous Symbol]
    uart_vision.o(.text.UART_Vision_ProcessData) refers to uart_vision.o(.bss.rx_index) for rx_index
    uart_vision.o(.text.UART_Vision_ProcessData) refers to uart_vision.o(.bss.rx_buffer) for rx_buffer
    uart_vision.o(.text.UART_Vision_ProcessData) refers to uart_vision.o(.bss.vision_display_buffer) for vision_display_buffer
    uart_vision.o(.text.UART_Vision_ProcessData) refers to strcpy.o(.text) for strcpy
    uart_vision.o(.ARM.exidx.text.UART_Vision_ProcessData) refers to uart_vision.o(.text.UART_Vision_ProcessData) for [Anonymous Symbol]
    timer7.o(.text.TIM7_Config) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer7.o(.text.TIM7_Config) refers to stm32f10x_tim.o(.text.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer7.o(.text.TIM7_Config) refers to stm32f10x_tim.o(.text.TIM_ITConfig) for TIM_ITConfig
    timer7.o(.text.TIM7_Config) refers to misc.o(.text.NVIC_Init) for NVIC_Init
    timer7.o(.text.TIM7_Config) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    timer7.o(.ARM.exidx.text.TIM7_Config) refers to timer7.o(.text.TIM7_Config) for [Anonymous Symbol]
    timer7.o(.text.TIM7_IRQHandler) refers to stm32f10x_tim.o(.text.TIM_GetITStatus) for TIM_GetITStatus
    timer7.o(.text.TIM7_IRQHandler) refers to stm32f10x_tim.o(.text.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    timer7.o(.text.TIM7_IRQHandler) refers to motor_control.o(.text.MotorControl_Update) for MotorControl_Update
    timer7.o(.ARM.exidx.text.TIM7_IRQHandler) refers to timer7.o(.text.TIM7_IRQHandler) for [Anonymous Symbol]
    oled.o(.text.OLED_W_SCL) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_W_SCL) refers to oled.o(.text.OLED_W_SCL) for [Anonymous Symbol]
    oled.o(.text.OLED_W_SDA) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_W_SDA) refers to oled.o(.text.OLED_W_SDA) for [Anonymous Symbol]
    oled.o(.text.OLED_GPIO_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(.text.OLED_GPIO_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    oled.o(.text.OLED_GPIO_Init) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_GPIO_Init) refers to oled.o(.text.OLED_GPIO_Init) for [Anonymous Symbol]
    oled.o(.text.OLED_I2C_Start) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_I2C_Start) refers to oled.o(.text.OLED_I2C_Start) for [Anonymous Symbol]
    oled.o(.text.OLED_I2C_Stop) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_I2C_Stop) refers to oled.o(.text.OLED_I2C_Stop) for [Anonymous Symbol]
    oled.o(.text.OLED_I2C_SendByte) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_I2C_SendByte) refers to oled.o(.text.OLED_I2C_SendByte) for [Anonymous Symbol]
    oled.o(.text.OLED_WriteCommand) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.OLED_WriteCommand) refers to oled.o(.text.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(.ARM.exidx.text.OLED_WriteCommand) refers to oled.o(.text.OLED_WriteCommand) for [Anonymous Symbol]
    oled.o(.text.OLED_WriteData) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.OLED_WriteData) refers to oled.o(.text.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(.ARM.exidx.text.OLED_WriteData) refers to oled.o(.text.OLED_WriteData) for [Anonymous Symbol]
    oled.o(.text.OLED_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(.text.OLED_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    oled.o(.text.OLED_Init) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(.text.OLED_Init) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_WriteData) for OLED_WriteData
    oled.o(.ARM.exidx.text.OLED_Init) refers to oled.o(.text.OLED_Init) for [Anonymous Symbol]
    oled.o(.text.OLED_Clear) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_Clear) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    oled.o(.ARM.exidx.text.OLED_Clear) refers to oled.o(.text.OLED_Clear) for [Anonymous Symbol]
    oled.o(.text.OLED_Update) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_Update) refers to oled.o(.text.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(.text.OLED_Update) refers to oled.o(.text.OLED_WriteData) for OLED_WriteData
    oled.o(.ARM.exidx.text.OLED_Update) refers to oled.o(.text.OLED_Update) for [Anonymous Symbol]
    oled.o(.text.OLED_SetCursor) refers to oled.o(.text.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(.ARM.exidx.text.OLED_SetCursor) refers to oled.o(.text.OLED_SetCursor) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_Pow) refers to oled.o(.text.OLED_Pow) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_pnpoly) refers to oled.o(.text.OLED_pnpoly) for [Anonymous Symbol]
    oled.o(.text.OLED_IsInAngle) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(.text.OLED_IsInAngle) refers to atan2.o(i.atan2) for atan2
    oled.o(.text.OLED_IsInAngle) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    oled.o(.text.OLED_IsInAngle) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(.text.OLED_IsInAngle) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    oled.o(.ARM.exidx.text.OLED_IsInAngle) refers to oled.o(.text.OLED_IsInAngle) for [Anonymous Symbol]
    oled.o(.text.OLED_UpdateArea) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_UpdateArea) refers to oled.o(.text.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(.text.OLED_UpdateArea) refers to oled.o(.text.OLED_WriteData) for OLED_WriteData
    oled.o(.ARM.exidx.text.OLED_UpdateArea) refers to oled.o(.text.OLED_UpdateArea) for [Anonymous Symbol]
    oled.o(.text.OLED_ClearArea) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_ClearArea) refers to oled.o(.text.OLED_ClearArea) for [Anonymous Symbol]
    oled.o(.text.OLED_Reverse) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_Reverse) refers to oled.o(.text.OLED_Reverse) for [Anonymous Symbol]
    oled.o(.text.OLED_ReverseArea) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_ReverseArea) refers to oled.o(.text.OLED_ReverseArea) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowChar) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowChar) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowChar) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.ARM.exidx.text.OLED_ShowChar) refers to oled.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowImage) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_ShowImage) refers to oled.o(.text.OLED_ShowImage) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowString) refers to oled_data.o(.rodata.OLED_CF16x16) for OLED_CF16x16
    oled.o(.text.OLED_ShowString) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.text.OLED_ShowString) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowString) refers to strcmpv7m.o(.text) for strcmp
    oled.o(.ARM.exidx.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowString) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowNum) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowNum) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.text.OLED_ShowNum) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.ARM.exidx.text.OLED_ShowNum) refers to oled.o(.text.OLED_ShowNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowSignedNum) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowSignedNum) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowSignedNum) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.ARM.exidx.text.OLED_ShowSignedNum) refers to oled.o(.text.OLED_ShowSignedNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowHexNum) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.text.OLED_ShowHexNum) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowHexNum) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.ARM.exidx.text.OLED_ShowHexNum) refers to oled.o(.text.OLED_ShowHexNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowBinNum) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowBinNum) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.text.OLED_ShowBinNum) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.ARM.exidx.text.OLED_ShowBinNum) refers to oled.o(.text.OLED_ShowBinNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowFloatNum) refers to dcmp.o(x$fpl$fcmp) for __aeabi_dcmplt
    oled.o(.text.OLED_ShowFloatNum) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowFloatNum) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowFloatNum) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.text.OLED_ShowFloatNum) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    oled.o(.text.OLED_ShowFloatNum) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(.text.OLED_ShowFloatNum) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    oled.o(.text.OLED_ShowFloatNum) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(.text.OLED_ShowFloatNum) refers to round.o(i.round) for round
    oled.o(.ARM.exidx.text.OLED_ShowFloatNum) refers to oled.o(.text.OLED_ShowFloatNum) for [Anonymous Symbol]
    oled.o(.text.OLED_Printf) refers to vsprintf.o(.text) for vsprintf
    oled.o(.text.OLED_Printf) refers to oled.o(.text.OLED_ShowString) for OLED_ShowString
    oled.o(.ARM.exidx.text.OLED_Printf) refers to oled.o(.text.OLED_Printf) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawPoint) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_DrawPoint) refers to oled.o(.text.OLED_DrawPoint) for [Anonymous Symbol]
    oled.o(.text.OLED_GetPoint) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_GetPoint) refers to oled.o(.text.OLED_GetPoint) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawLine) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_DrawLine) refers to oled.o(.text.OLED_DrawLine) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawRectangle) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_DrawRectangle) refers to oled.o(.text.OLED_DrawRectangle) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawTriangle) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_DrawTriangle) refers to oled.o(.text.OLED_DrawLine) for OLED_DrawLine
    oled.o(.ARM.exidx.text.OLED_DrawTriangle) refers to oled.o(.text.OLED_DrawTriangle) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawCircle) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_DrawCircle) refers to oled.o(.text.OLED_DrawCircle) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawEllipse) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(.text.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    oled.o(.text.OLED_DrawEllipse) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(.text.OLED_DrawEllipse) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    oled.o(.text.OLED_DrawEllipse) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_DrawEllipse) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(.text.OLED_DrawEllipse) refers to dcmp.o(x$fpl$fcmp) for __aeabi_dcmple
    oled.o(.text.OLED_DrawEllipse) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmpgt
    oled.o(.text.OLED_DrawEllipse) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    oled.o(.text.OLED_DrawEllipse) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    oled.o(.text.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    oled.o(.ARM.exidx.text.OLED_DrawEllipse) refers to oled.o(.text.OLED_DrawEllipse) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawArc) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(.text.OLED_DrawArc) refers to atan2.o(i.atan2) for atan2
    oled.o(.text.OLED_DrawArc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    oled.o(.text.OLED_DrawArc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(.text.OLED_DrawArc) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    oled.o(.text.OLED_DrawArc) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_DrawArc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(.ARM.exidx.text.OLED_DrawArc) refers to oled.o(.text.OLED_DrawArc) for [Anonymous Symbol]
    encoder.o(.text.EXTI0_IRQHandler) refers to stm32f10x_exti.o(.text.EXTI_GetITStatus) for EXTI_GetITStatus
    encoder.o(.text.EXTI0_IRQHandler) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(.text.EXTI0_IRQHandler) refers to encoder.o(.bss.encoder) for encoder
    encoder.o(.text.EXTI0_IRQHandler) refers to encoder.o(.data.path) for path
    encoder.o(.text.EXTI0_IRQHandler) refers to encoder.o(.bss.target_absolute_count) for target_absolute_count
    encoder.o(.text.EXTI0_IRQHandler) refers to motor_control.o(.text.MotorControl_SetSpeed) for MotorControl_SetSpeed
    encoder.o(.text.EXTI0_IRQHandler) refers to servo.o(.text.Left) for Left
    encoder.o(.text.EXTI0_IRQHandler) refers to servo.o(.text.Straight) for Straight
    encoder.o(.text.EXTI0_IRQHandler) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for GPIO_ResetBits
    encoder.o(.text.EXTI0_IRQHandler) refers to servo.o(.text.Right) for Right
    encoder.o(.text.EXTI0_IRQHandler) refers to stm32f10x_exti.o(.text.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(.ARM.exidx.text.EXTI0_IRQHandler) refers to encoder.o(.text.EXTI0_IRQHandler) for [Anonymous Symbol]
    encoder.o(.text.EXTI2_IRQHandler) refers to stm32f10x_exti.o(.text.EXTI_GetITStatus) for EXTI_GetITStatus
    encoder.o(.text.EXTI2_IRQHandler) refers to stm32f10x_tim.o(.text.TIM_GetCounter) for TIM_GetCounter
    encoder.o(.text.EXTI2_IRQHandler) refers to encoder.o(.data.EXTI2_IRQHandler.last_tim6_count) for EXTI2_IRQHandler.last_tim6_count
    encoder.o(.text.EXTI2_IRQHandler) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(.text.EXTI2_IRQHandler) refers to encoder.o(.bss.encoder) for encoder
    encoder.o(.text.EXTI2_IRQHandler) refers to stm32f10x_exti.o(.text.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(.ARM.exidx.text.EXTI2_IRQHandler) refers to encoder.o(.text.EXTI2_IRQHandler) for [Anonymous Symbol]
    encoder.o(.text.TIM6_IRQHandler) refers to stm32f10x_tim.o(.text.TIM_GetITStatus) for TIM_GetITStatus
    encoder.o(.text.TIM6_IRQHandler) refers to encoder.o(.bss.encoder) for encoder
    encoder.o(.text.TIM6_IRQHandler) refers to encoder.o(.bss.TIM6_IRQHandler.last_absolute_count) for TIM6_IRQHandler.last_absolute_count
    encoder.o(.text.TIM6_IRQHandler) refers to stm32f10x_tim.o(.text.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    encoder.o(.ARM.exidx.text.TIM6_IRQHandler) refers to encoder.o(.text.TIM6_IRQHandler) for [Anonymous Symbol]
    encoder.o(.text.Encoder_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(.text.Encoder_Init) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    encoder.o(.text.Encoder_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    encoder.o(.text.Encoder_Init) refers to stm32f10x_gpio.o(.text.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    encoder.o(.text.Encoder_Init) refers to stm32f10x_exti.o(.text.EXTI_Init) for EXTI_Init
    encoder.o(.text.Encoder_Init) refers to misc.o(.text.NVIC_Init) for NVIC_Init
    encoder.o(.text.Encoder_Init) refers to stm32f10x_tim.o(.text.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(.text.Encoder_Init) refers to stm32f10x_tim.o(.text.TIM_ITConfig) for TIM_ITConfig
    encoder.o(.text.Encoder_Init) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    encoder.o(.ARM.exidx.text.Encoder_Init) refers to encoder.o(.text.Encoder_Init) for [Anonymous Symbol]
    encoder.o(.text.Encoder_GetData) refers to encoder.o(.bss.encoder) for encoder
    encoder.o(.ARM.exidx.text.Encoder_GetData) refers to encoder.o(.text.Encoder_GetData) for [Anonymous Symbol]
    encoder.o(.text.Show_Encoder) refers to encoder.o(.bss.encoder) for encoder
    encoder.o(.text.Show_Encoder) refers to oled.o(.text.OLED_ShowSignedNum) for OLED_ShowSignedNum
    encoder.o(.text.Show_Encoder) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    encoder.o(.text.Show_Encoder) refers to oled.o(.text.OLED_ShowFloatNum) for OLED_ShowFloatNum
    encoder.o(.text.Show_Encoder) refers to oled.o(.text.OLED_Update) for OLED_Update
    encoder.o(.ARM.exidx.text.Show_Encoder) refers to encoder.o(.text.Show_Encoder) for [Anonymous Symbol]
    encoder.o(.text.EXTI1_IRQHandler) refers to stm32f10x_exti.o(.text.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(.ARM.exidx.text.EXTI1_IRQHandler) refers to encoder.o(.text.EXTI1_IRQHandler) for [Anonymous Symbol]
    encoder.o(.text.SetMotorWithRotation) refers to encoder.o(.bss.target_absolute_count) for target_absolute_count
    encoder.o(.ARM.exidx.text.SetMotorWithRotation) refers to encoder.o(.text.SetMotorWithRotation) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.PID_Init) refers to pid.o(.text.PID_Init) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.PID_SetTarget) refers to pid.o(.text.PID_SetTarget) for [Anonymous Symbol]
    pid.o(.text.PID_Update) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    pid.o(.text.PID_Update) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid.o(.text.PID_Update) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    pid.o(.text.PID_Update) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmpgt
    pid.o(.ARM.exidx.text.PID_Update) refers to pid.o(.text.PID_Update) for [Anonymous Symbol]
    servo.o(.text.Servo_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    servo.o(.text.Servo_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    servo.o(.text.Servo_Init) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for GPIO_ResetBits
    servo.o(.text.Servo_Init) refers to stm32f10x_gpio.o(.text.GPIO_SetBits) for GPIO_SetBits
    servo.o(.text.Servo_Init) refers to pwm2.o(.text.PWM2_Init) for PWM2_Init
    servo.o(.text.Servo_Init) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Servo_Init) refers to servo.o(.text.Servo_Init) for [Anonymous Symbol]
    servo.o(.text.Servo_SetAngle) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    servo.o(.text.Servo_SetAngle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(.text.Servo_SetAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(.text.Servo_SetAngle) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Servo_SetAngle) refers to servo.o(.text.Servo_SetAngle) for [Anonymous Symbol]
    servo.o(.text.Servo2_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    servo.o(.text.Servo2_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    servo.o(.text.Servo2_Init) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for GPIO_ResetBits
    servo.o(.text.Servo2_Init) refers to stm32f10x_gpio.o(.text.GPIO_SetBits) for GPIO_SetBits
    servo.o(.text.Servo2_Init) refers to pwm2.o(.text.PWM2_Init) for PWM2_Init
    servo.o(.text.Servo2_Init) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Servo2_Init) refers to servo.o(.text.Servo2_Init) for [Anonymous Symbol]
    servo.o(.text.Servo2_SetAngle) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    servo.o(.text.Servo2_SetAngle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(.text.Servo2_SetAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(.text.Servo2_SetAngle) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Servo2_SetAngle) refers to servo.o(.text.Servo2_SetAngle) for [Anonymous Symbol]
    servo.o(.text.Servo3_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    servo.o(.text.Servo3_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    servo.o(.text.Servo3_Init) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for GPIO_ResetBits
    servo.o(.text.Servo3_Init) refers to stm32f10x_gpio.o(.text.GPIO_SetBits) for GPIO_SetBits
    servo.o(.text.Servo3_Init) refers to pwm2.o(.text.PWM2_Init) for PWM2_Init
    servo.o(.text.Servo3_Init) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Servo3_Init) refers to servo.o(.text.Servo3_Init) for [Anonymous Symbol]
    servo.o(.text.Servo3_SetAngle) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    servo.o(.text.Servo3_SetAngle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(.text.Servo3_SetAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(.text.Servo3_SetAngle) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Servo3_SetAngle) refers to servo.o(.text.Servo3_SetAngle) for [Anonymous Symbol]
    servo.o(.text.Servo4_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    servo.o(.text.Servo4_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    servo.o(.text.Servo4_Init) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for GPIO_ResetBits
    servo.o(.text.Servo4_Init) refers to stm32f10x_gpio.o(.text.GPIO_SetBits) for GPIO_SetBits
    servo.o(.text.Servo4_Init) refers to pwm2.o(.text.PWM2_Init) for PWM2_Init
    servo.o(.text.Servo4_Init) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Servo4_Init) refers to servo.o(.text.Servo4_Init) for [Anonymous Symbol]
    servo.o(.text.Servo4_SetAngle) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    servo.o(.text.Servo4_SetAngle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(.text.Servo4_SetAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(.text.Servo4_SetAngle) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Servo4_SetAngle) refers to servo.o(.text.Servo4_SetAngle) for [Anonymous Symbol]
    servo.o(.text.Left) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Left) refers to servo.o(.text.Left) for [Anonymous Symbol]
    servo.o(.text.Right) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Right) refers to servo.o(.text.Right) for [Anonymous Symbol]
    servo.o(.text.Straight) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Straight) refers to servo.o(.text.Straight) for [Anonymous Symbol]
    servo.o(.text.servo_update) refers to uart_vision.o(.bss.vision_display_buffer) for vision_display_buffer
    servo.o(.text.servo_update) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.servo_update) refers to servo.o(.text.servo_update) for [Anonymous Symbol]
    motor_control.o(.text.Motor_Config) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    motor_control.o(.text.Motor_Config) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    motor_control.o(.text.Motor_Config) refers to stm32f10x_gpio.o(.text.GPIO_SetBits) for GPIO_SetBits
    motor_control.o(.text.Motor_Config) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for GPIO_ResetBits
    motor_control.o(.text.Motor_Config) refers to motor_control.o(.bss.motor) for motor
    motor_control.o(.text.Motor_Config) refers to pid.o(.text.PID_Init) for PID_Init
    motor_control.o(.text.Motor_Config) refers to pid.o(.text.PID_SetTarget) for PID_SetTarget
    motor_control.o(.ARM.exidx.text.Motor_Config) refers to motor_control.o(.text.Motor_Config) for [Anonymous Symbol]
    motor_control.o(.text.MotorControl_SetSpeed) refers to motor_control.o(.bss.motor) for motor
    motor_control.o(.text.MotorControl_SetSpeed) refers to pid.o(.text.PID_SetTarget) for PID_SetTarget
    motor_control.o(.ARM.exidx.text.MotorControl_SetSpeed) refers to motor_control.o(.text.MotorControl_SetSpeed) for [Anonymous Symbol]
    motor_control.o(.text.MotorControl_Update) refers to encoder.o(.text.Encoder_GetData) for Encoder_GetData
    motor_control.o(.text.MotorControl_Update) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    motor_control.o(.text.MotorControl_Update) refers to motor_control.o(.bss.motor) for motor
    motor_control.o(.text.MotorControl_Update) refers to pid.o(.text.PID_Update) for PID_Update
    motor_control.o(.text.MotorControl_Update) refers to pwm1.o(.text.PWM_SetDutyCycle) for PWM_SetDutyCycle
    motor_control.o(.ARM.exidx.text.MotorControl_Update) refers to motor_control.o(.text.MotorControl_Update) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_Init) refers to servo.o(.text.Servo_Init) for Servo_Init
    gimbal.o(.text.Gimbal_Init) refers to servo.o(.text.Servo2_Init) for Servo2_Init
    gimbal.o(.text.Gimbal_Init) refers to servo.o(.text.Servo3_Init) for Servo3_Init
    gimbal.o(.text.Gimbal_Init) refers to servo.o(.text.Servo4_Init) for Servo4_Init
    gimbal.o(.text.Gimbal_Init) refers to gimbal.o(.data.currentPos) for currentPos
    gimbal.o(.text.Gimbal_Init) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.text.Gimbal_Init) refers to servo.o(.text.Servo_SetAngle) for Servo_SetAngle
    gimbal.o(.text.Gimbal_Init) refers to servo.o(.text.Servo2_SetAngle) for Servo2_SetAngle
    gimbal.o(.text.Gimbal_Init) refers to servo.o(.text.Servo3_SetAngle) for Servo3_SetAngle
    gimbal.o(.text.Gimbal_Init) refers to servo.o(.text.Servo4_SetAngle) for Servo4_SetAngle
    gimbal.o(.ARM.exidx.text.Gimbal_Init) refers to gimbal.o(.text.Gimbal_Init) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_SetTargetPosition) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.ARM.exidx.text.Gimbal_SetTargetPosition) refers to gimbal.o(.text.Gimbal_SetTargetPosition) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_UpdatePosition) refers to gimbal.o(.data.currentPos) for currentPos
    gimbal.o(.text.Gimbal_UpdatePosition) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.text.Gimbal_UpdatePosition) refers to servo.o(.text.Servo_SetAngle) for Servo_SetAngle
    gimbal.o(.text.Gimbal_UpdatePosition) refers to servo.o(.text.Servo2_SetAngle) for Servo2_SetAngle
    gimbal.o(.ARM.exidx.text.Gimbal_UpdatePosition) refers to gimbal.o(.text.Gimbal_UpdatePosition) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_GetCurrentPosition) refers to gimbal.o(.data.currentPos) for currentPos
    gimbal.o(.ARM.exidx.text.Gimbal_GetCurrentPosition) refers to gimbal.o(.text.Gimbal_GetCurrentPosition) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_GetTargetPosition) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.ARM.exidx.text.Gimbal_GetTargetPosition) refers to gimbal.o(.text.Gimbal_GetTargetPosition) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_MoveXPlus) refers to gimbal.o(.data.currentPos) for currentPos
    gimbal.o(.text.Gimbal_MoveXPlus) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.ARM.exidx.text.Gimbal_MoveXPlus) refers to gimbal.o(.text.Gimbal_MoveXPlus) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_MoveXMinus) refers to gimbal.o(.data.currentPos) for currentPos
    gimbal.o(.text.Gimbal_MoveXMinus) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.ARM.exidx.text.Gimbal_MoveXMinus) refers to gimbal.o(.text.Gimbal_MoveXMinus) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_MoveYPlus) refers to gimbal.o(.data.currentPos) for currentPos
    gimbal.o(.text.Gimbal_MoveYPlus) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.ARM.exidx.text.Gimbal_MoveYPlus) refers to gimbal.o(.text.Gimbal_MoveYPlus) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_MoveYMinus) refers to gimbal.o(.data.currentPos) for currentPos
    gimbal.o(.text.Gimbal_MoveYMinus) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.ARM.exidx.text.Gimbal_MoveYMinus) refers to gimbal.o(.text.Gimbal_MoveYMinus) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_AdjustToRedTarget) refers to usart1.o(.data.redx) for redx
    gimbal.o(.text.Gimbal_AdjustToRedTarget) refers to gimbal.o(.data.currentPos) for currentPos
    gimbal.o(.text.Gimbal_AdjustToRedTarget) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.text.Gimbal_AdjustToRedTarget) refers to usart1.o(.data.redy) for redy
    gimbal.o(.ARM.exidx.text.Gimbal_AdjustToRedTarget) refers to gimbal.o(.text.Gimbal_AdjustToRedTarget) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_AdjustToCenterTarget) refers to usart1.o(.bss.centerx) for centerx
    gimbal.o(.text.Gimbal_AdjustToCenterTarget) refers to usart1.o(.bss.centery) for centery
    gimbal.o(.text.Gimbal_AdjustToCenterTarget) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.ARM.exidx.text.Gimbal_AdjustToCenterTarget) refers to gimbal.o(.text.Gimbal_AdjustToCenterTarget) for [Anonymous Symbol]
    vsprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dcmp.o(x$fpl$fcmp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(x$fpl$fcmp) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    dcmp.o(x$fpl$fcmp) refers to dgeqf.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(x$fpl$fcmp) refers to dleqf.o(x$fpl$dleqf) for _dcmple
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fcmp.o(x$fpl$fcmp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(x$fpl$fcmp) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(x$fpl$fcmp) refers to fgeqf.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(x$fpl$fcmp) refers to fleqf.o(x$fpl$fleqf) for _fcmple
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.atan2) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2.o(i.atan2) refers to _rserrno.o(.text) for __set_errno
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    round.o(i.round) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    round.o(i.round) refers to drnd.o(x$fpl$drnd) for _drnd
    round.o(i.round) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    round.o(i.round) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    round.o(i.round) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _c16rtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace.o(.text) for isspace
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dgeqf.o(x$fpl$dgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dgeqf.o(x$fpl$dgeqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dgeqf.o(x$fpl$dgeqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    drnd.o(x$fpl$drnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    feqf.o(x$fpl$feqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fgeqf.o(x$fpl$fgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fgeqf.o(x$fpl$fgeqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fgeqf.o(x$fpl$fgeqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.atan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec_accurate.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec_accurate.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec_accurate.o(.text) refers to btod_accurate.o(.text) for _btod_main
    _printf_fp_dec_accurate.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec_accurate.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    _c16rtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod_accurate.o(.text) refers to btod_accurate_common.o(.text) for _btod_common
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    btod_accurate_common.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    libinit2.o(.ARM.Collect$$libinit$$00000011) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display

