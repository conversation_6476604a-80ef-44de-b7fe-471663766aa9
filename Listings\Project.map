Component: ARM Compiler 5.05 update 1 (build 106) Tool: armlink [4d0efa]

==============================================================================

Section Cross References

    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to encoder.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to encoder.o(i.EXTI1_IRQHandler) for EXTI1_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to encoder.o(i.EXTI2_IRQHandler) for EXTI2_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to usart1.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to encoder.o(i.TIM6_IRQHandler) for TIM6_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to timer7.o(i.TIM7_IRQHandler) for TIM7_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    main.o(i.main) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    main.o(i.main) refers to gimbal.o(i.Gimbal_Init) for Gimbal_Init
    main.o(i.main) refers to usart1.o(i.USART1_Init) for USART1_Init
    main.o(i.main) refers to gimbal.o(i.Gimbal_SetTargetPosition) for Gimbal_SetTargetPosition
    main.o(i.main) refers to gimbal.o(i.Gimbal_AdjustToRedTarget) for Gimbal_AdjustToRedTarget
    main.o(i.main) refers to gimbal.o(i.Gimbal_UpdatePosition) for Gimbal_UpdatePosition
    main.o(i.main) refers to main.o(i.Delay_ms) for Delay_ms
    main.o(i.main) refers to usart1.o(.data) for centery
    pwm1.o(i.PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pwm1.o(i.PWM_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    pwm1.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    pwm1.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    pwm1.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    pwm1.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    pwm1.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    pwm1.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    pwm1.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    pwm1.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    pwm1.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    pwm1.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    pwm1.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm1.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    pwm1.o(i.PWM_Init) refers to pwm1.o(.data) for pwmPeriod
    pwm1.o(i.PWM_Init) refers to system_stm32f10x.o(.data) for SystemCoreClock
    pwm1.o(i.PWM_SetDeadTime) refers to stm32f10x_tim.o(i.TIM_BDTRStructInit) for TIM_BDTRStructInit
    pwm1.o(i.PWM_SetDeadTime) refers to stm32f10x_tim.o(i.TIM_BDTRConfig) for TIM_BDTRConfig
    pwm1.o(i.PWM_SetDutyCycle) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    pwm1.o(i.PWM_SetDutyCycle) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    pwm1.o(i.PWM_SetDutyCycle) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    pwm1.o(i.PWM_SetDutyCycle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pwm1.o(i.PWM_SetDutyCycle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    pwm1.o(i.PWM_SetDutyCycle) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    pwm1.o(i.PWM_SetDutyCycle) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    pwm1.o(i.PWM_SetDutyCycle) refers to stm32f10x_tim.o(i.TIM_SetCompare2) for TIM_SetCompare2
    pwm1.o(i.PWM_SetDutyCycle) refers to stm32f10x_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    pwm1.o(i.PWM_SetDutyCycle) refers to stm32f10x_tim.o(i.TIM_SetCompare4) for TIM_SetCompare4
    pwm1.o(i.PWM_SetDutyCycle) refers to pwm1.o(.data) for pwmPeriod
    pwm1.o(i.PWM_Start) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm1.o(i.PWM_Start) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    pwm1.o(i.PWM_Stop) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    pwm1.o(i.PWM_Stop) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm1.o(i.PwmConfig) refers to pwm1.o(i.PWM_Init) for PWM_Init
    pwm1.o(i.PwmConfig) refers to pwm1.o(i.PWM_SetDutyCycle) for PWM_SetDutyCycle
    pwm1.o(i.PwmConfig) refers to pwm1.o(i.PWM_Start) for PWM_Start
    pwm1.o(i.PwmConfig) refers to pwm1.o(.constdata) for .constdata
    pwm2.o(i.PWM2_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    pwm2.o(i.PWM2_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pwm2.o(i.PWM2_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    pwm2.o(i.PWM2_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    pwm2.o(i.PWM2_Init) refers to stm32f10x_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    pwm2.o(i.PWM2_Init) refers to stm32f10x_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    pwm2.o(i.PWM2_Init) refers to stm32f10x_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    pwm2.o(i.PWM2_Init) refers to stm32f10x_tim.o(i.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    pwm2.o(i.PWM2_Init) refers to stm32f10x_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    pwm2.o(i.PWM2_Init) refers to stm32f10x_tim.o(i.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    pwm2.o(i.PWM2_Init) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    pwm2.o(i.PWM2_Init) refers to stm32f10x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    pwm2.o(i.PWM2_Init) refers to stm32f10x_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    pwm2.o(i.PWM2_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm2.o(i.PWM2_Init) refers to pwm2.o(.data) for pwmPeriod
    pwm2.o(i.PWM2_Init) refers to system_stm32f10x.o(.data) for SystemCoreClock
    pwm2.o(i.PWM2_SetDutyCycle) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    pwm2.o(i.PWM2_SetDutyCycle) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    pwm2.o(i.PWM2_SetDutyCycle) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    pwm2.o(i.PWM2_SetDutyCycle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pwm2.o(i.PWM2_SetDutyCycle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    pwm2.o(i.PWM2_SetDutyCycle) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    pwm2.o(i.PWM2_SetDutyCycle) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    pwm2.o(i.PWM2_SetDutyCycle) refers to stm32f10x_tim.o(i.TIM_SetCompare2) for TIM_SetCompare2
    pwm2.o(i.PWM2_SetDutyCycle) refers to stm32f10x_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    pwm2.o(i.PWM2_SetDutyCycle) refers to stm32f10x_tim.o(i.TIM_SetCompare4) for TIM_SetCompare4
    pwm2.o(i.PWM2_SetDutyCycle) refers to pwm2.o(.data) for pwmPeriod
    pwm2.o(i.PWM2_Start) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm2.o(i.PWM2_Stop) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm2.o(i.Pwm2Config_Channel4) refers to pwm2.o(i.PWM2_Init) for PWM2_Init
    pwm2.o(i.Pwm2Config_Channel4) refers to pwm2.o(i.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    pwm2.o(i.Pwm2Config_Channel4) refers to pwm2.o(i.PWM2_Start) for PWM2_Start
    pwm2.o(i.Pwm2Config_Channel4) refers to pwm2.o(.constdata) for .constdata
    usart1.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart1.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart1.o(i.USART1_IRQHandler) refers to usart1.o(i.parse_received_data) for parse_received_data
    usart1.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart1.o(i.USART1_IRQHandler) refers to usart1.o(.data) for rx_index
    usart1.o(i.USART1_IRQHandler) refers to usart1.o(.bss) for rx_buffer
    usart1.o(i.USART1_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart1.o(i.USART1_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart1.o(i.USART1_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart1.o(i.USART1_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart1.o(i.USART1_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart1.o(i.USART1_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart1.o(i.USART1_SendChar) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart1.o(i.USART1_SendChar) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart1.o(i.USART1_SendString) refers to usart1.o(i.USART1_SendChar) for USART1_SendChar
    usart1.o(i.parse_received_data) refers to _scanf_int.o(.text) for _scanf_int
    usart1.o(i.parse_received_data) refers to __0sscanf.o(.text) for __0sscanf
    usart1.o(i.parse_received_data) refers to usart1.o(.data) for redy
    uart_vision.o(i.UART_Vision_Init) refers to rt_memclr.o(.text) for __aeabi_memclr
    uart_vision.o(i.UART_Vision_Init) refers to uart_vision.o(.bss) for vision_display_buffer
    uart_vision.o(i.UART_Vision_Init) refers to uart_vision.o(.data) for rx_index
    uart_vision.o(i.UART_Vision_ProcessData) refers to strcpy.o(.text) for strcpy
    uart_vision.o(i.UART_Vision_ProcessData) refers to uart_vision.o(.data) for rx_index
    uart_vision.o(i.UART_Vision_ProcessData) refers to uart_vision.o(.bss) for rx_buffer
    timer7.o(i.TIM7_Config) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer7.o(i.TIM7_Config) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer7.o(i.TIM7_Config) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer7.o(i.TIM7_Config) refers to misc.o(i.NVIC_Init) for NVIC_Init
    timer7.o(i.TIM7_Config) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer7.o(i.TIM7_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    timer7.o(i.TIM7_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    timer7.o(i.TIM7_IRQHandler) refers to motor_control.o(i.MotorControl_Update) for MotorControl_Update
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ClearArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_IsInAngle) for OLED_IsInAngle
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    oled.o(i.OLED_DrawEllipse) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_DrawEllipse) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    oled.o(i.OLED_DrawEllipse) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    oled.o(i.OLED_DrawEllipse) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    oled.o(i.OLED_DrawEllipse) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    oled.o(i.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    oled.o(i.OLED_DrawEllipse) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_DrawRectangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawLine) for OLED_DrawLine
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_pnpoly) for OLED_pnpoly
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_GPIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_GPIO_Init) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_GPIO_Init) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_GetPoint) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_I2C_SendByte) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_SendByte) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_I2C_Start) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_Start) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_I2C_Stop) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_Stop) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_GPIO_Init) for OLED_GPIO_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Update) for OLED_Update
    oled.o(i.OLED_IsInAngle) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_IsInAngle) refers to atan2.o(i.atan2) for atan2
    oled.o(i.OLED_IsInAngle) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    oled.o(i.OLED_IsInAngle) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_IsInAngle) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    oled.o(i.OLED_Printf) refers to vsprintf.o(.text) for vsprintf
    oled.o(i.OLED_Printf) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_Reverse) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ReverseArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowChar) refers to oled_data.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowFloatNum) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowFloatNum) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    oled.o(i.OLED_ShowFloatNum) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(i.OLED_ShowFloatNum) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowFloatNum) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_ShowFloatNum) refers to round.o(i.round) for round
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowImage) refers to oled.o(i.OLED_ClearArea) for OLED_ClearArea
    oled.o(i.OLED_ShowImage) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to strcmpv7m.o(.text) for strcmp
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowString) refers to oled_data.o(.constdata) for OLED_CF16x16
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_Update) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_UpdateArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_W_SCL) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_W_SDA) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    encoder.o(i.EXTI0_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    encoder.o(i.EXTI0_IRQHandler) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(i.EXTI0_IRQHandler) refers to motor_control.o(i.MotorControl_SetSpeed) for MotorControl_SetSpeed
    encoder.o(i.EXTI0_IRQHandler) refers to servo.o(i.Left) for Left
    encoder.o(i.EXTI0_IRQHandler) refers to servo.o(i.Straight) for Straight
    encoder.o(i.EXTI0_IRQHandler) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    encoder.o(i.EXTI0_IRQHandler) refers to servo.o(i.Right) for Right
    encoder.o(i.EXTI0_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(i.EXTI0_IRQHandler) refers to encoder.o(.bss) for encoder
    encoder.o(i.EXTI0_IRQHandler) refers to encoder.o(.data) for path
    encoder.o(i.EXTI1_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(i.EXTI2_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    encoder.o(i.EXTI2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetCounter) for TIM_GetCounter
    encoder.o(i.EXTI2_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(i.EXTI2_IRQHandler) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(i.EXTI2_IRQHandler) refers to encoder.o(.data) for last_tim6_count
    encoder.o(i.EXTI2_IRQHandler) refers to encoder.o(.bss) for encoder
    encoder.o(i.Encoder_GetData) refers to encoder.o(.bss) for encoder
    encoder.o(i.Encoder_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(i.Encoder_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    encoder.o(i.Encoder_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.Encoder_Init) refers to stm32f10x_gpio.o(i.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    encoder.o(i.Encoder_Init) refers to stm32f10x_exti.o(i.EXTI_Init) for EXTI_Init
    encoder.o(i.Encoder_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    encoder.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    encoder.o(i.Encoder_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    encoder.o(i.SetMotorWithRotation) refers to encoder.o(.data) for target_absolute_count
    encoder.o(i.Show_Encoder) refers to encoder.o(i.Encoder_GetData) for Encoder_GetData
    encoder.o(i.Show_Encoder) refers to oled.o(i.OLED_ShowSignedNum) for OLED_ShowSignedNum
    encoder.o(i.Show_Encoder) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    encoder.o(i.Show_Encoder) refers to oled.o(i.OLED_ShowFloatNum) for OLED_ShowFloatNum
    encoder.o(i.Show_Encoder) refers to oled.o(i.OLED_Update) for OLED_Update
    encoder.o(i.TIM6_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    encoder.o(i.TIM6_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    encoder.o(i.TIM6_IRQHandler) refers to encoder.o(.bss) for encoder
    encoder.o(i.TIM6_IRQHandler) refers to encoder.o(.data) for last_absolute_count
    pid.o(i.PID_Update) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    pid.o(i.PID_Update) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid.o(i.PID_Update) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    pid.o(i.PID_Update) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    pid.o(i.PID_Update) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    servo.o(i.Left) refers to servo.o(i.Servo_SetAngle) for Servo_SetAngle
    servo.o(i.Right) refers to servo.o(i.Servo_SetAngle) for Servo_SetAngle
    servo.o(i.Servo2_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    servo.o(i.Servo2_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    servo.o(i.Servo2_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    servo.o(i.Servo2_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    servo.o(i.Servo2_Init) refers to pwm2.o(i.PWM2_Init) for PWM2_Init
    servo.o(i.Servo2_Init) refers to servo.o(i.Servo2_SetAngle) for Servo2_SetAngle
    servo.o(i.Servo2_Init) refers to servo.o(.constdata) for .constdata
    servo.o(i.Servo2_SetAngle) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    servo.o(i.Servo2_SetAngle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(i.Servo2_SetAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(i.Servo2_SetAngle) refers to pwm2.o(i.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(i.Servo3_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    servo.o(i.Servo3_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    servo.o(i.Servo3_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    servo.o(i.Servo3_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    servo.o(i.Servo3_Init) refers to pwm2.o(i.PWM2_Init) for PWM2_Init
    servo.o(i.Servo3_Init) refers to servo.o(i.Servo3_SetAngle) for Servo3_SetAngle
    servo.o(i.Servo3_Init) refers to servo.o(.constdata) for .constdata
    servo.o(i.Servo3_SetAngle) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    servo.o(i.Servo3_SetAngle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(i.Servo3_SetAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(i.Servo3_SetAngle) refers to pwm2.o(i.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(i.Servo4_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    servo.o(i.Servo4_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    servo.o(i.Servo4_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    servo.o(i.Servo4_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    servo.o(i.Servo4_Init) refers to pwm2.o(i.PWM2_Init) for PWM2_Init
    servo.o(i.Servo4_Init) refers to servo.o(i.Servo4_SetAngle) for Servo4_SetAngle
    servo.o(i.Servo4_Init) refers to servo.o(.constdata) for .constdata
    servo.o(i.Servo4_SetAngle) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    servo.o(i.Servo4_SetAngle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(i.Servo4_SetAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(i.Servo4_SetAngle) refers to pwm2.o(i.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(i.Servo_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    servo.o(i.Servo_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    servo.o(i.Servo_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    servo.o(i.Servo_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    servo.o(i.Servo_Init) refers to pwm2.o(i.PWM2_Init) for PWM2_Init
    servo.o(i.Servo_Init) refers to servo.o(i.Servo_SetAngle) for Servo_SetAngle
    servo.o(i.Servo_Init) refers to servo.o(.constdata) for .constdata
    servo.o(i.Servo_SetAngle) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    servo.o(i.Servo_SetAngle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(i.Servo_SetAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(i.Servo_SetAngle) refers to pwm2.o(i.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(i.Straight) refers to servo.o(i.Servo_SetAngle) for Servo_SetAngle
    servo.o(i.servo_update) refers to servo.o(i.Left) for Left
    servo.o(i.servo_update) refers to servo.o(i.Right) for Right
    servo.o(i.servo_update) refers to servo.o(i.Straight) for Straight
    servo.o(i.servo_update) refers to uart_vision.o(.bss) for vision_display_buffer
    motor_control.o(i.MotorControl_SetSpeed) refers to pid.o(i.PID_SetTarget) for PID_SetTarget
    motor_control.o(i.MotorControl_SetSpeed) refers to motor_control.o(.bss) for motor
    motor_control.o(i.MotorControl_Update) refers to encoder.o(i.Encoder_GetData) for Encoder_GetData
    motor_control.o(i.MotorControl_Update) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    motor_control.o(i.MotorControl_Update) refers to pid.o(i.PID_Update) for PID_Update
    motor_control.o(i.MotorControl_Update) refers to pwm1.o(i.PWM_SetDutyCycle) for PWM_SetDutyCycle
    motor_control.o(i.MotorControl_Update) refers to motor_control.o(.bss) for motor
    motor_control.o(i.Motor_Config) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    motor_control.o(i.Motor_Config) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    motor_control.o(i.Motor_Config) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor_control.o(i.Motor_Config) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor_control.o(i.Motor_Config) refers to pid.o(i.PID_Init) for PID_Init
    motor_control.o(i.Motor_Config) refers to motor_control.o(i.MotorControl_SetSpeed) for MotorControl_SetSpeed
    motor_control.o(i.Motor_Config) refers to motor_control.o(.bss) for motor
    gimbal.o(i.Gimbal_AdjustToRedTarget) refers to gimbal.o(i.Gimbal_MoveXPlus) for Gimbal_MoveXPlus
    gimbal.o(i.Gimbal_AdjustToRedTarget) refers to gimbal.o(i.Gimbal_MoveXMinus) for Gimbal_MoveXMinus
    gimbal.o(i.Gimbal_AdjustToRedTarget) refers to gimbal.o(i.Gimbal_MoveYPlus) for Gimbal_MoveYPlus
    gimbal.o(i.Gimbal_AdjustToRedTarget) refers to gimbal.o(i.Gimbal_MoveYMinus) for Gimbal_MoveYMinus
    gimbal.o(i.Gimbal_AdjustToRedTarget) refers to usart1.o(.data) for redx
    gimbal.o(i.Gimbal_GetCurrentPosition) refers to gimbal.o(.data) for currentPos
    gimbal.o(i.Gimbal_GetTargetPosition) refers to gimbal.o(.data) for targetPos
    gimbal.o(i.Gimbal_Init) refers to servo.o(i.Servo_Init) for Servo_Init
    gimbal.o(i.Gimbal_Init) refers to servo.o(i.Servo2_Init) for Servo2_Init
    gimbal.o(i.Gimbal_Init) refers to servo.o(i.Servo3_Init) for Servo3_Init
    gimbal.o(i.Gimbal_Init) refers to servo.o(i.Servo4_Init) for Servo4_Init
    gimbal.o(i.Gimbal_Init) refers to servo.o(i.Servo_SetAngle) for Servo_SetAngle
    gimbal.o(i.Gimbal_Init) refers to servo.o(i.Servo2_SetAngle) for Servo2_SetAngle
    gimbal.o(i.Gimbal_Init) refers to servo.o(i.Servo3_SetAngle) for Servo3_SetAngle
    gimbal.o(i.Gimbal_Init) refers to servo.o(i.Servo4_SetAngle) for Servo4_SetAngle
    gimbal.o(i.Gimbal_Init) refers to gimbal.o(.data) for currentPos
    gimbal.o(i.Gimbal_MoveXMinus) refers to gimbal.o(i.Gimbal_GetCurrentPosition) for Gimbal_GetCurrentPosition
    gimbal.o(i.Gimbal_MoveXMinus) refers to gimbal.o(i.Gimbal_SetTargetPosition) for Gimbal_SetTargetPosition
    gimbal.o(i.Gimbal_MoveXPlus) refers to gimbal.o(i.Gimbal_GetCurrentPosition) for Gimbal_GetCurrentPosition
    gimbal.o(i.Gimbal_MoveXPlus) refers to gimbal.o(i.Gimbal_SetTargetPosition) for Gimbal_SetTargetPosition
    gimbal.o(i.Gimbal_MoveYMinus) refers to gimbal.o(i.Gimbal_GetCurrentPosition) for Gimbal_GetCurrentPosition
    gimbal.o(i.Gimbal_MoveYMinus) refers to gimbal.o(i.Gimbal_SetTargetPosition) for Gimbal_SetTargetPosition
    gimbal.o(i.Gimbal_MoveYPlus) refers to gimbal.o(i.Gimbal_GetCurrentPosition) for Gimbal_GetCurrentPosition
    gimbal.o(i.Gimbal_MoveYPlus) refers to gimbal.o(i.Gimbal_SetTargetPosition) for Gimbal_SetTargetPosition
    gimbal.o(i.Gimbal_SetTargetPosition) refers to gimbal.o(.data) for targetPos
    gimbal.o(i.Gimbal_UpdatePosition) refers to servo.o(i.Servo_SetAngle) for Servo_SetAngle
    gimbal.o(i.Gimbal_UpdatePosition) refers to servo.o(i.Servo2_SetAngle) for Servo2_SetAngle
    gimbal.o(i.Gimbal_UpdatePosition) refers to gimbal.o(.data) for currentPos
    vsprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    frleqf.o(x$fpl$frleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frleqf.o(x$fpl$frleqf) refers to fleqf.o(x$fpl$fleqf) for __fpl_fcmple_InfNaN
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    round.o(i.round) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    round.o(i.round) refers to drnd.o(x$fpl$drnd) for _drnd
    round.o(i.round) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    round.o(i.round) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    round.o(i.round) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace.o(.text) for isspace
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.atan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.__atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (332 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (60 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (148 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing pwm1.o(i.PWM_Init), (392 bytes).
    Removing pwm1.o(i.PWM_SetDeadTime), (40 bytes).
    Removing pwm1.o(i.PWM_Start), (24 bytes).
    Removing pwm1.o(i.PWM_Stop), (24 bytes).
    Removing pwm1.o(i.PwmConfig), (52 bytes).
    Removing pwm1.o(.constdata), (12 bytes).
    Removing pwm2.o(i.PWM2_Start), (12 bytes).
    Removing pwm2.o(i.PWM2_Stop), (14 bytes).
    Removing pwm2.o(i.Pwm2Config_Channel4), (52 bytes).
    Removing pwm2.o(.constdata), (16 bytes).
    Removing usart1.o(i.USART1_SendChar), (32 bytes).
    Removing usart1.o(i.USART1_SendString), (22 bytes).
    Removing uart_vision.o(i.UART_Vision_Init), (40 bytes).
    Removing uart_vision.o(i.UART_Vision_ProcessData), (108 bytes).
    Removing uart_vision.o(.bss), (64 bytes).
    Removing uart_vision.o(.data), (1 bytes).
    Removing timer7.o(i.TIM7_Config), (108 bytes).
    Removing oled.o(i.OLED_Clear), (40 bytes).
    Removing oled.o(i.OLED_ClearArea), (144 bytes).
    Removing oled.o(i.OLED_DrawArc), (618 bytes).
    Removing oled.o(i.OLED_DrawCircle), (352 bytes).
    Removing oled.o(i.OLED_DrawEllipse), (812 bytes).
    Removing oled.o(i.OLED_DrawLine), (374 bytes).
    Removing oled.o(i.OLED_DrawPoint), (80 bytes).
    Removing oled.o(i.OLED_DrawRectangle), (142 bytes).
    Removing oled.o(i.OLED_DrawTriangle), (232 bytes).
    Removing oled.o(i.OLED_GPIO_Init), (96 bytes).
    Removing oled.o(i.OLED_GetPoint), (68 bytes).
    Removing oled.o(i.OLED_I2C_SendByte), (62 bytes).
    Removing oled.o(i.OLED_I2C_Start), (28 bytes).
    Removing oled.o(i.OLED_I2C_Stop), (22 bytes).
    Removing oled.o(i.OLED_Init), (154 bytes).
    Removing oled.o(i.OLED_IsInAngle), (124 bytes).
    Removing oled.o(i.OLED_Pow), (20 bytes).
    Removing oled.o(i.OLED_Printf), (50 bytes).
    Removing oled.o(i.OLED_Reverse), (52 bytes).
    Removing oled.o(i.OLED_ReverseArea), (144 bytes).
    Removing oled.o(i.OLED_SetCursor), (34 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (70 bytes).
    Removing oled.o(i.OLED_ShowChar), (84 bytes).
    Removing oled.o(i.OLED_ShowFloatNum), (210 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (96 bytes).
    Removing oled.o(i.OLED_ShowImage), (252 bytes).
    Removing oled.o(i.OLED_ShowNum), (76 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (112 bytes).
    Removing oled.o(i.OLED_ShowString), (272 bytes).
    Removing oled.o(i.OLED_Update), (40 bytes).
    Removing oled.o(i.OLED_UpdateArea), (124 bytes).
    Removing oled.o(i.OLED_W_SCL), (24 bytes).
    Removing oled.o(i.OLED_W_SDA), (24 bytes).
    Removing oled.o(i.OLED_WriteCommand), (32 bytes).
    Removing oled.o(i.OLED_WriteData), (46 bytes).
    Removing oled.o(i.OLED_pnpoly), (140 bytes).
    Removing oled.o(.bss), (1024 bytes).
    Removing oled_data.o(.constdata), (2367 bytes).
    Removing encoder.o(i.Encoder_Init), (244 bytes).
    Removing encoder.o(i.SetMotorWithRotation), (12 bytes).
    Removing encoder.o(i.Show_Encoder), (66 bytes).
    Removing pid.o(i.PID_Init), (26 bytes).
    Removing servo.o(i.servo_update), (56 bytes).
    Removing motor_control.o(i.Motor_Config), (128 bytes).
    Removing gimbal.o(i.Gimbal_GetTargetPosition), (16 bytes).

502 unused section(s) (total 27428 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/drnd.s                          0x00000000   Number         0  drnd.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/frleqf.s                        0x00000000   Number         0  frleqf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    Hardware\OLED.c                          0x00000000   Number         0  oled.o ABSOLUTE
    Hardware\OLED_Data.c                     0x00000000   Number         0  oled_data.o ABSOLUTE
    Hardware\encoder.c                       0x00000000   Number         0  encoder.o ABSOLUTE
    Hardware\gimbal.c                        0x00000000   Number         0  gimbal.o ABSOLUTE
    Hardware\motor_control.c                 0x00000000   Number         0  motor_control.o ABSOLUTE
    Hardware\servo.c                         0x00000000   Number         0  servo.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f10x_adc.c                  0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Library\stm32f10x_bkp.c                  0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    Library\stm32f10x_can.c                  0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    Library\stm32f10x_cec.c                  0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    Library\stm32f10x_crc.c                  0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    Library\stm32f10x_dac.c                  0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Library\stm32f10x_dbgmcu.c               0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    Library\stm32f10x_dma.c                  0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    Library\stm32f10x_exti.c                 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Library\stm32f10x_flash.c                0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    Library\stm32f10x_fsmc.c                 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    Library\stm32f10x_gpio.c                 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Library\stm32f10x_i2c.c                  0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    Library\stm32f10x_iwdg.c                 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Library\stm32f10x_pwr.c                  0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Library\stm32f10x_rcc.c                  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Library\stm32f10x_rtc.c                  0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    Library\stm32f10x_sdio.c                 0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    Library\stm32f10x_spi.c                  0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    Library\stm32f10x_tim.c                  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Library\stm32f10x_usart.c                0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Library\stm32f10x_wwdg.c                 0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    Start\\core_cm3.c                        0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\core_cm3.c                         0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\startup_stm32f10x_hd.s             0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    Start\system_stm32f10x.c                 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    System\pid.c                             0x00000000   Number         0  pid.o ABSOLUTE
    System\pwm1.c                            0x00000000   Number         0  pwm1.o ABSOLUTE
    System\pwm2.c                            0x00000000   Number         0  pwm2.o ABSOLUTE
    System\timer7.c                          0x00000000   Number         0  timer7.o ABSOLUTE
    System\uart_vision.c                     0x00000000   Number         0  uart_vision.o ABSOLUTE
    System\usart1.c                          0x00000000   Number         0  usart1.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001a4   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080001a6   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080001ac   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080001ac   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080001b8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001b8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001b8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001b8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001b8   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001b8   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001b8   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001b8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001b8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001b8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001b8   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001b8   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001b8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001b8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001b8   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001ba   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000003      0x080001bc   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    .ARM.Collect$$libshutdown$$00000006      0x080001bc   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001bc   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000B      0x080001bc   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    .ARM.Collect$$libshutdown$$0000000E      0x080001bc   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$0000000F      0x080001bc   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$rtentry$$00000000          0x080001be   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001be   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001be   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001c4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001c4   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001c8   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001c8   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001d0   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001d2   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001d2   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001d6   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001dc   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x0800021c   Section        0  __0sscanf.o(.text)
    .text                                    0x08000258   Section        0  _scanf_int.o(.text)
    .text                                    0x080003a4   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000424   Section        0  heapauxi.o(.text)
    .text                                    0x0800042a   Section        0  _chval.o(.text)
    .text                                    0x08000448   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x08000449   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x08000474   Section        0  _sgetc.o(.text)
    .text                                    0x080004b4   Section        0  isspace.o(.text)
    .text                                    0x080004c8   Section        0  _scanf.o(.text)
    .text                                    0x0800083c   Section        8  libspace.o(.text)
    .text                                    0x08000844   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000890   Section       16  rt_ctype_table.o(.text)
    .text                                    0x080008a0   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080008a8   Section        0  exit.o(.text)
    .text                                    0x080008b4   Section        0  sys_exit.o(.text)
    .text                                    0x080008c0   Section        2  use_no_semi.o(.text)
    .text                                    0x080008c2   Section        0  indicate_semi.o(.text)
    i.BusFault_Handler                       0x080008c2   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x080008c6   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Delay_ms                               0x080008c8   Section        0  main.o(i.Delay_ms)
    i.EXTI0_IRQHandler                       0x080008e0   Section        0  encoder.o(i.EXTI0_IRQHandler)
    i.EXTI1_IRQHandler                       0x080009d8   Section        0  encoder.o(i.EXTI1_IRQHandler)
    i.EXTI2_IRQHandler                       0x080009e4   Section        0  encoder.o(i.EXTI2_IRQHandler)
    i.EXTI_ClearITPendingBit                 0x08000a60   Section        0  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    i.EXTI_GetITStatus                       0x08000a6c   Section        0  stm32f10x_exti.o(i.EXTI_GetITStatus)
    i.Encoder_GetData                        0x08000a94   Section        0  encoder.o(i.Encoder_GetData)
    i.GPIO_Init                              0x08000ac0   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ReadInputDataBit                  0x08000bd6   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x08000be8   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08000bec   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.Gimbal_AdjustToRedTarget               0x08000bf0   Section        0  gimbal.o(i.Gimbal_AdjustToRedTarget)
    i.Gimbal_GetCurrentPosition              0x08000c34   Section        0  gimbal.o(i.Gimbal_GetCurrentPosition)
    i.Gimbal_Init                            0x08000c44   Section        0  gimbal.o(i.Gimbal_Init)
    i.Gimbal_MoveXMinus                      0x08000c88   Section        0  gimbal.o(i.Gimbal_MoveXMinus)
    i.Gimbal_MoveXPlus                       0x08000ca2   Section        0  gimbal.o(i.Gimbal_MoveXPlus)
    i.Gimbal_MoveYMinus                      0x08000cbc   Section        0  gimbal.o(i.Gimbal_MoveYMinus)
    i.Gimbal_MoveYPlus                       0x08000cd6   Section        0  gimbal.o(i.Gimbal_MoveYPlus)
    i.Gimbal_SetTargetPosition               0x08000cf0   Section        0  gimbal.o(i.Gimbal_SetTargetPosition)
    i.Gimbal_UpdatePosition                  0x08000d1c   Section        0  gimbal.o(i.Gimbal_UpdatePosition)
    i.HardFault_Handler                      0x08000e30   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.Left                                   0x08000e34   Section        0  servo.o(i.Left)
    i.MemManage_Handler                      0x08000e3e   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.MotorControl_SetSpeed                  0x08000e44   Section        0  motor_control.o(i.MotorControl_SetSpeed)
    i.MotorControl_Update                    0x08000e5c   Section        0  motor_control.o(i.MotorControl_Update)
    i.NMI_Handler                            0x08000e88   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08000e8c   Section        0  misc.o(i.NVIC_Init)
    i.PID_SetTarget                          0x08000efc   Section        0  pid.o(i.PID_SetTarget)
    i.PID_Update                             0x08000f06   Section        0  pid.o(i.PID_Update)
    i.PWM2_Init                              0x08000f94   Section        0  pwm2.o(i.PWM2_Init)
    i.PWM2_SetDutyCycle                      0x08001100   Section        0  pwm2.o(i.PWM2_SetDutyCycle)
    i.PWM_SetDutyCycle                       0x08001190   Section        0  pwm1.o(i.PWM_SetDutyCycle)
    i.PendSV_Handler                         0x0800121c   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB1PeriphClockCmd                 0x08001220   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08001240   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08001260   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.Right                                  0x08001334   Section        0  servo.o(i.Right)
    i.SVC_Handler                            0x0800133e   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.Servo2_Init                            0x08001340   Section        0  servo.o(i.Servo2_Init)
    i.Servo2_SetAngle                        0x080013a4   Section        0  servo.o(i.Servo2_SetAngle)
    i.Servo3_Init                            0x080013ec   Section        0  servo.o(i.Servo3_Init)
    i.Servo3_SetAngle                        0x08001464   Section        0  servo.o(i.Servo3_SetAngle)
    i.Servo4_Init                            0x080014ac   Section        0  servo.o(i.Servo4_Init)
    i.Servo4_SetAngle                        0x08001514   Section        0  servo.o(i.Servo4_SetAngle)
    i.Servo_Init                             0x0800155c   Section        0  servo.o(i.Servo_Init)
    i.Servo_SetAngle                         0x080015c0   Section        0  servo.o(i.Servo_SetAngle)
    i.SetSysClock                            0x08001608   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08001609   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08001610   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08001611   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.Straight                               0x080016f0   Section        0  servo.o(i.Straight)
    i.SysTick_Handler                        0x080016fa   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x080016fc   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM6_IRQHandler                        0x0800175c   Section        0  encoder.o(i.TIM6_IRQHandler)
    i.TIM7_IRQHandler                        0x080017a8   Section        0  timer7.o(i.TIM7_IRQHandler)
    i.TIM_ARRPreloadConfig                   0x080017c8   Section        0  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    i.TIM_ClearITPendingBit                  0x080017e0   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x080017e6   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_GetCounter                         0x080017fe   Section        0  stm32f10x_tim.o(i.TIM_GetCounter)
    i.TIM_GetITStatus                        0x08001804   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_OC1Init                            0x08001828   Section        0  stm32f10x_tim.o(i.TIM_OC1Init)
    i.TIM_OC1PreloadConfig                   0x080018c0   Section        0  stm32f10x_tim.o(i.TIM_OC1PreloadConfig)
    i.TIM_OC2Init                            0x080018d4   Section        0  stm32f10x_tim.o(i.TIM_OC2Init)
    i.TIM_OC2PreloadConfig                   0x08001978   Section        0  stm32f10x_tim.o(i.TIM_OC2PreloadConfig)
    i.TIM_OC3Init                            0x08001994   Section        0  stm32f10x_tim.o(i.TIM_OC3Init)
    i.TIM_OC3PreloadConfig                   0x08001a34   Section        0  stm32f10x_tim.o(i.TIM_OC3PreloadConfig)
    i.TIM_OC4Init                            0x08001a48   Section        0  stm32f10x_tim.o(i.TIM_OC4Init)
    i.TIM_OC4PreloadConfig                   0x08001ac4   Section        0  stm32f10x_tim.o(i.TIM_OC4PreloadConfig)
    i.TIM_SetCompare1                        0x08001ade   Section        0  stm32f10x_tim.o(i.TIM_SetCompare1)
    i.TIM_SetCompare2                        0x08001ae2   Section        0  stm32f10x_tim.o(i.TIM_SetCompare2)
    i.TIM_SetCompare3                        0x08001ae6   Section        0  stm32f10x_tim.o(i.TIM_SetCompare3)
    i.TIM_SetCompare4                        0x08001aea   Section        0  stm32f10x_tim.o(i.TIM_SetCompare4)
    i.TIM_TimeBaseInit                       0x08001af0   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.USART1_IRQHandler                      0x08001b94   Section        0  usart1.o(i.USART1_IRQHandler)
    i.USART1_Init                            0x08001c0c   Section        0  usart1.o(i.USART1_Init)
    i.USART_ClearITPendingBit                0x08001cac   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x08001cca   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetITStatus                      0x08001ce2   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08001d36   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08001d80   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08001e58   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.UsageFault_Handler                     0x08001e62   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.main                                   0x08001e68   Section        0  main.o(i.main)
    i.parse_received_data                    0x08001ea4   Section        0  usart1.o(i.parse_received_data)
    locale$$code                             0x08001f40   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$fadd                               0x08001f6c   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x08001f7b   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcmpinf                            0x08002030   Section       24  fcmpi.o(x$fpl$fcmpinf)
    x$fpl$fdiv                               0x08002048   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x08002049   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$ffixu                              0x080021cc   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$fflt                               0x0800220c   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$ffltu                              0x0800223c   Section       38  fflt_clz.o(x$fpl$ffltu)
    x$fpl$fleqf                              0x08002264   Section      104  fleqf.o(x$fpl$fleqf)
    x$fpl$fmul                               0x080022cc   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x080023ce   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x0800245a   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$frleqf                             0x08002464   Section       98  frleqf.o(x$fpl$frleqf)
    x$fpl$fsub                               0x080024c8   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x080024d7   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$usenofp                            0x080025b2   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x080025b4   Section       60  servo.o(.constdata)
    locale$$data                             0x08002610   Section      272  lc_ctype_c.o(locale$$data)
    __lcctype_c_name                         0x08002614   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x0800261c   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x08002720   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000000   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000010   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000014   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000028   Section        2  pwm1.o(.data)
    pwmPeriod                                0x20000028   Data           2  pwm1.o(.data)
    .data                                    0x2000002a   Section        2  pwm2.o(.data)
    pwmPeriod                                0x2000002a   Data           2  pwm2.o(.data)
    .data                                    0x2000002c   Section       26  usart1.o(.data)
    rx_index                                 0x20000044   Data           2  usart1.o(.data)
    .data                                    0x20000048   Section       16  encoder.o(.data)
    last_tim6_count                          0x20000050   Data           2  encoder.o(.data)
    last_absolute_count                      0x20000054   Data           4  encoder.o(.data)
    .data                                    0x20000058   Section        8  gimbal.o(.data)
    .bss                                     0x20000060   Section      256  usart1.o(.bss)
    rx_buffer                                0x20000060   Data         256  usart1.o(.bss)
    .bss                                     0x20000160   Section       16  encoder.o(.bss)
    encoder                                  0x20000160   Data          16  encoder.o(.bss)
    .bss                                     0x20000170   Section       40  motor_control.o(.bss)
    motor                                    0x20000170   Data          40  motor_control.o(.bss)
    .bss                                     0x20000198   Section       96  libspace.o(.bss)
    HEAP                                     0x200001f8   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x200001f8   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x200003f8   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x200003f8   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x200007f8   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_mbtowc                             - Undefined Weak Reference
    _scanf_real                               - Undefined Weak Reference
    _scanf_string                             - Undefined Weak Reference
    _scanf_wctomb                             - Undefined Weak Reference
    _scanf_wstring                            - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001a5   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080001ad   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080001ad   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_alloca_1                   0x080001b9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001b9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001b9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001b9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001b9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001b9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080001b9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001b9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_ctype_1                 0x080001b9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001b9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001b9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001b9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080001b9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001b9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001b9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080001bb   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_fp_trap_1              0x080001bd   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_heap_1                 0x080001bd   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_lib_shutdown_return                 0x080001bd   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_signal_1               0x080001bd   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_stdio_1                0x080001bd   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    __rt_lib_shutdown_user_alloc_1           0x080001bd   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    __rt_entry                               0x080001bf   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001bf   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001c5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001c5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001c9   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001c9   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001d1   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001d3   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001d3   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001d7   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001dd   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x080001f7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x080001f9   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __0sscanf                                0x0800021d   Thumb Code    52  __0sscanf.o(.text)
    _scanf_int                               0x08000259   Thumb Code   332  _scanf_int.o(.text)
    strcmp                                   0x080003a5   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x08000425   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000427   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000429   Thumb Code     2  heapauxi.o(.text)
    _chval                                   0x0800042b   Thumb Code    28  _chval.o(.text)
    __vfscanf_char                           0x08000455   Thumb Code    24  scanf_char.o(.text)
    _sgetc                                   0x08000475   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08000493   Thumb Code    34  _sgetc.o(.text)
    isspace                                  0x080004b5   Thumb Code    18  isspace.o(.text)
    __vfscanf                                0x080004c9   Thumb Code   878  _scanf.o(.text)
    __user_libspace                          0x0800083d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800083d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800083d   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000845   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08000891   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x080008a1   Thumb Code     8  rt_locale_intlibspace.o(.text)
    exit                                     0x080008a9   Thumb Code    12  exit.o(.text)
    _sys_exit                                0x080008b5   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x080008c1   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080008c1   Thumb Code     2  use_no_semi.o(.text)
    BusFault_Handler                         0x080008c3   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    __semihosting_library_function           0x080008c3   Thumb Code     0  indicate_semi.o(.text)
    DebugMon_Handler                         0x080008c7   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Delay_ms                                 0x080008c9   Thumb Code    24  main.o(i.Delay_ms)
    EXTI0_IRQHandler                         0x080008e1   Thumb Code   222  encoder.o(i.EXTI0_IRQHandler)
    EXTI1_IRQHandler                         0x080009d9   Thumb Code    10  encoder.o(i.EXTI1_IRQHandler)
    EXTI2_IRQHandler                         0x080009e5   Thumb Code   108  encoder.o(i.EXTI2_IRQHandler)
    EXTI_ClearITPendingBit                   0x08000a61   Thumb Code     6  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    EXTI_GetITStatus                         0x08000a6d   Thumb Code    34  stm32f10x_exti.o(i.EXTI_GetITStatus)
    Encoder_GetData                          0x08000a95   Thumb Code    40  encoder.o(i.Encoder_GetData)
    GPIO_Init                                0x08000ac1   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ReadInputDataBit                    0x08000bd7   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x08000be9   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08000bed   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    Gimbal_AdjustToRedTarget                 0x08000bf1   Thumb Code    60  gimbal.o(i.Gimbal_AdjustToRedTarget)
    Gimbal_GetCurrentPosition                0x08000c35   Thumb Code    10  gimbal.o(i.Gimbal_GetCurrentPosition)
    Gimbal_Init                              0x08000c45   Thumb Code    58  gimbal.o(i.Gimbal_Init)
    Gimbal_MoveXMinus                        0x08000c89   Thumb Code    26  gimbal.o(i.Gimbal_MoveXMinus)
    Gimbal_MoveXPlus                         0x08000ca3   Thumb Code    26  gimbal.o(i.Gimbal_MoveXPlus)
    Gimbal_MoveYMinus                        0x08000cbd   Thumb Code    26  gimbal.o(i.Gimbal_MoveYMinus)
    Gimbal_MoveYPlus                         0x08000cd7   Thumb Code    26  gimbal.o(i.Gimbal_MoveYPlus)
    Gimbal_SetTargetPosition                 0x08000cf1   Thumb Code    40  gimbal.o(i.Gimbal_SetTargetPosition)
    Gimbal_UpdatePosition                    0x08000d1d   Thumb Code   268  gimbal.o(i.Gimbal_UpdatePosition)
    HardFault_Handler                        0x08000e31   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    Left                                     0x08000e35   Thumb Code    10  servo.o(i.Left)
    MemManage_Handler                        0x08000e3f   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    MotorControl_SetSpeed                    0x08000e45   Thumb Code    18  motor_control.o(i.MotorControl_SetSpeed)
    MotorControl_Update                      0x08000e5d   Thumb Code    40  motor_control.o(i.MotorControl_Update)
    NMI_Handler                              0x08000e89   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08000e8d   Thumb Code   100  misc.o(i.NVIC_Init)
    PID_SetTarget                            0x08000efd   Thumb Code    10  pid.o(i.PID_SetTarget)
    PID_Update                               0x08000f07   Thumb Code   142  pid.o(i.PID_Update)
    PWM2_Init                                0x08000f95   Thumb Code   350  pwm2.o(i.PWM2_Init)
    PWM2_SetDutyCycle                        0x08001101   Thumb Code   134  pwm2.o(i.PWM2_SetDutyCycle)
    PWM_SetDutyCycle                         0x08001191   Thumb Code   126  pwm1.o(i.PWM_SetDutyCycle)
    PendSV_Handler                           0x0800121d   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x08001221   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08001241   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08001261   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    Right                                    0x08001335   Thumb Code    10  servo.o(i.Right)
    SVC_Handler                              0x0800133f   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    Servo2_Init                              0x08001341   Thumb Code    90  servo.o(i.Servo2_Init)
    Servo2_SetAngle                          0x080013a5   Thumb Code    64  servo.o(i.Servo2_SetAngle)
    Servo3_Init                              0x080013ed   Thumb Code   106  servo.o(i.Servo3_Init)
    Servo3_SetAngle                          0x08001465   Thumb Code    64  servo.o(i.Servo3_SetAngle)
    Servo4_Init                              0x080014ad   Thumb Code    96  servo.o(i.Servo4_Init)
    Servo4_SetAngle                          0x08001515   Thumb Code    64  servo.o(i.Servo4_SetAngle)
    Servo_Init                               0x0800155d   Thumb Code    90  servo.o(i.Servo_Init)
    Servo_SetAngle                           0x080015c1   Thumb Code    64  servo.o(i.Servo_SetAngle)
    Straight                                 0x080016f1   Thumb Code    10  servo.o(i.Straight)
    SysTick_Handler                          0x080016fb   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x080016fd   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    TIM6_IRQHandler                          0x0800175d   Thumb Code    62  encoder.o(i.TIM6_IRQHandler)
    TIM7_IRQHandler                          0x080017a9   Thumb Code    26  timer7.o(i.TIM7_IRQHandler)
    TIM_ARRPreloadConfig                     0x080017c9   Thumb Code    24  stm32f10x_tim.o(i.TIM_ARRPreloadConfig)
    TIM_ClearITPendingBit                    0x080017e1   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x080017e7   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_GetCounter                           0x080017ff   Thumb Code     6  stm32f10x_tim.o(i.TIM_GetCounter)
    TIM_GetITStatus                          0x08001805   Thumb Code    34  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_OC1Init                              0x08001829   Thumb Code   132  stm32f10x_tim.o(i.TIM_OC1Init)
    TIM_OC1PreloadConfig                     0x080018c1   Thumb Code    18  stm32f10x_tim.o(i.TIM_OC1PreloadConfig)
    TIM_OC2Init                              0x080018d5   Thumb Code   154  stm32f10x_tim.o(i.TIM_OC2Init)
    TIM_OC2PreloadConfig                     0x08001979   Thumb Code    26  stm32f10x_tim.o(i.TIM_OC2PreloadConfig)
    TIM_OC3Init                              0x08001995   Thumb Code   150  stm32f10x_tim.o(i.TIM_OC3Init)
    TIM_OC3PreloadConfig                     0x08001a35   Thumb Code    18  stm32f10x_tim.o(i.TIM_OC3PreloadConfig)
    TIM_OC4Init                              0x08001a49   Thumb Code   114  stm32f10x_tim.o(i.TIM_OC4Init)
    TIM_OC4PreloadConfig                     0x08001ac5   Thumb Code    26  stm32f10x_tim.o(i.TIM_OC4PreloadConfig)
    TIM_SetCompare1                          0x08001adf   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCompare1)
    TIM_SetCompare2                          0x08001ae3   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCompare2)
    TIM_SetCompare3                          0x08001ae7   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCompare3)
    TIM_SetCompare4                          0x08001aeb   Thumb Code     6  stm32f10x_tim.o(i.TIM_SetCompare4)
    TIM_TimeBaseInit                         0x08001af1   Thumb Code   122  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    USART1_IRQHandler                        0x08001b95   Thumb Code   106  usart1.o(i.USART1_IRQHandler)
    USART1_Init                              0x08001c0d   Thumb Code   152  usart1.o(i.USART1_Init)
    USART_ClearITPendingBit                  0x08001cad   Thumb Code    30  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x08001ccb   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetITStatus                        0x08001ce3   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08001d37   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08001d81   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08001e59   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    UsageFault_Handler                       0x08001e63   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    main                                     0x08001e69   Thumb Code    52  main.o(i.main)
    parse_received_data                      0x08001ea5   Thumb Code    92  usart1.o(i.parse_received_data)
    _get_lc_ctype                            0x08001f41   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_fadd                             0x08001f6d   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x08001f6d   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __fpl_fcmp_Inf                           0x08002031   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __aeabi_fdiv                             0x08002049   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x08002049   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_f2uiz                            0x080021cd   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x080021cd   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __aeabi_i2f                              0x0800220d   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x0800220d   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_ui2f                             0x0800223d   Thumb Code     0  fflt_clz.o(x$fpl$ffltu)
    _ffltu                                   0x0800223d   Thumb Code    38  fflt_clz.o(x$fpl$ffltu)
    __aeabi_cfcmple                          0x08002265   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    _fcmple                                  0x08002265   Thumb Code   104  fleqf.o(x$fpl$fleqf)
    __fpl_fcmple_InfNaN                      0x080022b7   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    __aeabi_fmul                             0x080022cd   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x080022cd   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x080023cf   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x0800245b   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_cfrcmple                         0x08002465   Thumb Code     0  frleqf.o(x$fpl$frleqf)
    _frcmple                                 0x08002465   Thumb Code    98  frleqf.o(x$fpl$frleqf)
    __aeabi_fsub                             0x080024c9   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x080024c9   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    __I$use$fp                               0x080025b2   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x080025f0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002610   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x0800261d   Data           0  lc_ctype_c.o(locale$$data)
    SystemCoreClock                          0x20000014   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000018   Data          16  system_stm32f10x.o(.data)
    redx                                     0x2000002c   Data           2  usart1.o(.data)
    redy                                     0x2000002e   Data           2  usart1.o(.data)
    centerx                                  0x20000030   Data           2  usart1.o(.data)
    centery                                  0x20000032   Data           2  usart1.o(.data)
    vertex_x                                 0x20000034   Data           8  usart1.o(.data)
    vertex_y                                 0x2000003c   Data           8  usart1.o(.data)
    target_absolute_count                    0x20000048   Data           4  encoder.o(.data)
    path                                     0x2000004c   Data           4  encoder.o(.data)
    currentPos                               0x20000058   Data           4  gimbal.o(.data)
    targetPos                                0x2000005c   Data           4  gimbal.o(.data)
    __libspace_start                         0x20000198   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200001f8   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002780, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00002720, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO         3274    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000008   Code   RO         4473  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x00000034   Code   RO         4927    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0800016c   0x0000001a   Code   RO         4929    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x0000001c   Code   RO         4931    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x080001a4   0x00000002   Code   RO         4790    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4801    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4803    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4806    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4808    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4810    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000006   Code   RO         4811    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080001ac   0x080001ac   0x00000000   Code   RO         4813    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001ac   0x080001ac   0x0000000c   Code   RO         4814    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080001b8   0x080001b8   0x00000000   Code   RO         4815    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001b8   0x080001b8   0x00000000   Code   RO         4817    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001b8   0x080001b8   0x00000000   Code   RO         4819    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001b8   0x080001b8   0x00000000   Code   RO         4821    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001b8   0x080001b8   0x00000000   Code   RO         4823    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001b8   0x080001b8   0x00000000   Code   RO         4825    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001b8   0x080001b8   0x00000000   Code   RO         4827    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001b8   0x080001b8   0x00000000   Code   RO         4829    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001b8   0x080001b8   0x00000000   Code   RO         4831    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001b8   0x080001b8   0x00000000   Code   RO         4833    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001b8   0x080001b8   0x00000000   Code   RO         4837    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001b8   0x080001b8   0x00000000   Code   RO         4839    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001b8   0x080001b8   0x00000000   Code   RO         4841    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001b8   0x080001b8   0x00000000   Code   RO         4843    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001b8   0x080001b8   0x00000002   Code   RO         4844    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001ba   0x080001ba   0x00000002   Code   RO         4868    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         4882    .ARM.Collect$$libshutdown$$00000003  c_w.l(libshutdown2.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         4885    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         4888    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         4890    .ARM.Collect$$libshutdown$$0000000B  c_w.l(libshutdown2.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         4893    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001bc   0x080001bc   0x00000002   Code   RO         4894    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080001be   0x080001be   0x00000000   Code   RO         4543    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001be   0x080001be   0x00000000   Code   RO         4694    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001be   0x080001be   0x00000006   Code   RO         4706    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001c4   0x080001c4   0x00000000   Code   RO         4696    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001c4   0x080001c4   0x00000004   Code   RO         4697    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001c8   0x080001c8   0x00000000   Code   RO         4699    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001c8   0x080001c8   0x00000008   Code   RO         4700    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001d0   0x080001d0   0x00000002   Code   RO         4795    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO         4848    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001d2   0x080001d2   0x00000004   Code   RO         4849    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001d6   0x080001d6   0x00000006   Code   RO         4850    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001dc   0x080001dc   0x00000040   Code   RO         3275    .text               startup_stm32f10x_hd.o
    0x0800021c   0x0800021c   0x0000003c   Code   RO         4461    .text               c_w.l(__0sscanf.o)
    0x08000258   0x08000258   0x0000014c   Code   RO         4463    .text               c_w.l(_scanf_int.o)
    0x080003a4   0x080003a4   0x00000080   Code   RO         4469    .text               c_w.l(strcmpv7m.o)
    0x08000424   0x08000424   0x00000006   Code   RO         4471    .text               c_w.l(heapauxi.o)
    0x0800042a   0x0800042a   0x0000001c   Code   RO         4637    .text               c_w.l(_chval.o)
    0x08000446   0x08000446   0x00000002   PAD
    0x08000448   0x08000448   0x0000002c   Code   RO         4639    .text               c_w.l(scanf_char.o)
    0x08000474   0x08000474   0x00000040   Code   RO         4641    .text               c_w.l(_sgetc.o)
    0x080004b4   0x080004b4   0x00000012   Code   RO         4715    .text               c_w.l(isspace.o)
    0x080004c6   0x080004c6   0x00000002   PAD
    0x080004c8   0x080004c8   0x00000374   Code   RO         4731    .text               c_w.l(_scanf.o)
    0x0800083c   0x0800083c   0x00000008   Code   RO         4741    .text               c_w.l(libspace.o)
    0x08000844   0x08000844   0x0000004a   Code   RO         4744    .text               c_w.l(sys_stackheap_outer.o)
    0x0800088e   0x0800088e   0x00000002   PAD
    0x08000890   0x08000890   0x00000010   Code   RO         4746    .text               c_w.l(rt_ctype_table.o)
    0x080008a0   0x080008a0   0x00000008   Code   RO         4751    .text               c_w.l(rt_locale_intlibspace.o)
    0x080008a8   0x080008a8   0x0000000c   Code   RO         4783    .text               c_w.l(exit.o)
    0x080008b4   0x080008b4   0x0000000c   Code   RO         4860    .text               c_w.l(sys_exit.o)
    0x080008c0   0x080008c0   0x00000002   Code   RO         4871    .text               c_w.l(use_no_semi.o)
    0x080008c2   0x080008c2   0x00000000   Code   RO         4873    .text               c_w.l(indicate_semi.o)
    0x080008c2   0x080008c2   0x00000004   Code   RO         3390    i.BusFault_Handler  stm32f10x_it.o
    0x080008c6   0x080008c6   0x00000002   Code   RO         3391    i.DebugMon_Handler  stm32f10x_it.o
    0x080008c8   0x080008c8   0x00000018   Code   RO         3279    i.Delay_ms          main.o
    0x080008e0   0x080008e0   0x000000f8   Code   RO         3987    i.EXTI0_IRQHandler  encoder.o
    0x080009d8   0x080009d8   0x0000000a   Code   RO         3988    i.EXTI1_IRQHandler  encoder.o
    0x080009e2   0x080009e2   0x00000002   PAD
    0x080009e4   0x080009e4   0x0000007c   Code   RO         3989    i.EXTI2_IRQHandler  encoder.o
    0x08000a60   0x08000a60   0x0000000c   Code   RO          994    i.EXTI_ClearITPendingBit  stm32f10x_exti.o
    0x08000a6c   0x08000a6c   0x00000028   Code   RO          998    i.EXTI_GetITStatus  stm32f10x_exti.o
    0x08000a94   0x08000a94   0x0000002c   Code   RO         3990    i.Encoder_GetData   encoder.o
    0x08000ac0   0x08000ac0   0x00000116   Code   RO         1359    i.GPIO_Init         stm32f10x_gpio.o
    0x08000bd6   0x08000bd6   0x00000012   Code   RO         1363    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x08000be8   0x08000be8   0x00000004   Code   RO         1366    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x08000bec   0x08000bec   0x00000004   Code   RO         1367    i.GPIO_SetBits      stm32f10x_gpio.o
    0x08000bf0   0x08000bf0   0x00000044   Code   RO         4382    i.Gimbal_AdjustToRedTarget  gimbal.o
    0x08000c34   0x08000c34   0x00000010   Code   RO         4383    i.Gimbal_GetCurrentPosition  gimbal.o
    0x08000c44   0x08000c44   0x00000044   Code   RO         4385    i.Gimbal_Init       gimbal.o
    0x08000c88   0x08000c88   0x0000001a   Code   RO         4386    i.Gimbal_MoveXMinus  gimbal.o
    0x08000ca2   0x08000ca2   0x0000001a   Code   RO         4387    i.Gimbal_MoveXPlus  gimbal.o
    0x08000cbc   0x08000cbc   0x0000001a   Code   RO         4388    i.Gimbal_MoveYMinus  gimbal.o
    0x08000cd6   0x08000cd6   0x0000001a   Code   RO         4389    i.Gimbal_MoveYPlus  gimbal.o
    0x08000cf0   0x08000cf0   0x0000002c   Code   RO         4390    i.Gimbal_SetTargetPosition  gimbal.o
    0x08000d1c   0x08000d1c   0x00000114   Code   RO         4391    i.Gimbal_UpdatePosition  gimbal.o
    0x08000e30   0x08000e30   0x00000004   Code   RO         3392    i.HardFault_Handler  stm32f10x_it.o
    0x08000e34   0x08000e34   0x0000000a   Code   RO         4174    i.Left              servo.o
    0x08000e3e   0x08000e3e   0x00000004   Code   RO         3393    i.MemManage_Handler  stm32f10x_it.o
    0x08000e42   0x08000e42   0x00000002   PAD
    0x08000e44   0x08000e44   0x00000018   Code   RO         4260    i.MotorControl_SetSpeed  motor_control.o
    0x08000e5c   0x08000e5c   0x0000002c   Code   RO         4261    i.MotorControl_Update  motor_control.o
    0x08000e88   0x08000e88   0x00000002   Code   RO         3394    i.NMI_Handler       stm32f10x_it.o
    0x08000e8a   0x08000e8a   0x00000002   PAD
    0x08000e8c   0x08000e8c   0x00000070   Code   RO          311    i.NVIC_Init         misc.o
    0x08000efc   0x08000efc   0x0000000a   Code   RO         4148    i.PID_SetTarget     pid.o
    0x08000f06   0x08000f06   0x0000008e   Code   RO         4149    i.PID_Update        pid.o
    0x08000f94   0x08000f94   0x0000016c   Code   RO         3584    i.PWM2_Init         pwm2.o
    0x08001100   0x08001100   0x00000090   Code   RO         3585    i.PWM2_SetDutyCycle  pwm2.o
    0x08001190   0x08001190   0x0000008c   Code   RO         3538    i.PWM_SetDutyCycle  pwm1.o
    0x0800121c   0x0800121c   0x00000002   Code   RO         3395    i.PendSV_Handler    stm32f10x_it.o
    0x0800121e   0x0800121e   0x00000002   PAD
    0x08001220   0x08001220   0x00000020   Code   RO         1787    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08001240   0x08001240   0x00000020   Code   RO         1789    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08001260   0x08001260   0x000000d4   Code   RO         1797    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08001334   0x08001334   0x0000000a   Code   RO         4175    i.Right             servo.o
    0x0800133e   0x0800133e   0x00000002   Code   RO         3396    i.SVC_Handler       stm32f10x_it.o
    0x08001340   0x08001340   0x00000064   Code   RO         4176    i.Servo2_Init       servo.o
    0x080013a4   0x080013a4   0x00000048   Code   RO         4177    i.Servo2_SetAngle   servo.o
    0x080013ec   0x080013ec   0x00000078   Code   RO         4178    i.Servo3_Init       servo.o
    0x08001464   0x08001464   0x00000048   Code   RO         4179    i.Servo3_SetAngle   servo.o
    0x080014ac   0x080014ac   0x00000068   Code   RO         4180    i.Servo4_Init       servo.o
    0x08001514   0x08001514   0x00000048   Code   RO         4181    i.Servo4_SetAngle   servo.o
    0x0800155c   0x0800155c   0x00000064   Code   RO         4182    i.Servo_Init        servo.o
    0x080015c0   0x080015c0   0x00000048   Code   RO         4183    i.Servo_SetAngle    servo.o
    0x08001608   0x08001608   0x00000008   Code   RO         3208    i.SetSysClock       system_stm32f10x.o
    0x08001610   0x08001610   0x000000e0   Code   RO         3209    i.SetSysClockTo72   system_stm32f10x.o
    0x080016f0   0x080016f0   0x0000000a   Code   RO         4184    i.Straight          servo.o
    0x080016fa   0x080016fa   0x00000002   Code   RO         3397    i.SysTick_Handler   stm32f10x_it.o
    0x080016fc   0x080016fc   0x00000060   Code   RO         3211    i.SystemInit        system_stm32f10x.o
    0x0800175c   0x0800175c   0x0000004c   Code   RO         3994    i.TIM6_IRQHandler   encoder.o
    0x080017a8   0x080017a8   0x00000020   Code   RO         3706    i.TIM7_IRQHandler   timer7.o
    0x080017c8   0x080017c8   0x00000018   Code   RO         2421    i.TIM_ARRPreloadConfig  stm32f10x_tim.o
    0x080017e0   0x080017e0   0x00000006   Code   RO         2428    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x080017e6   0x080017e6   0x00000018   Code   RO         2433    i.TIM_Cmd           stm32f10x_tim.o
    0x080017fe   0x080017fe   0x00000006   Code   RO         2452    i.TIM_GetCounter    stm32f10x_tim.o
    0x08001804   0x08001804   0x00000022   Code   RO         2454    i.TIM_GetITStatus   stm32f10x_tim.o
    0x08001826   0x08001826   0x00000002   PAD
    0x08001828   0x08001828   0x00000098   Code   RO         2462    i.TIM_OC1Init       stm32f10x_tim.o
    0x080018c0   0x080018c0   0x00000012   Code   RO         2465    i.TIM_OC1PreloadConfig  stm32f10x_tim.o
    0x080018d2   0x080018d2   0x00000002   PAD
    0x080018d4   0x080018d4   0x000000a4   Code   RO         2467    i.TIM_OC2Init       stm32f10x_tim.o
    0x08001978   0x08001978   0x0000001a   Code   RO         2470    i.TIM_OC2PreloadConfig  stm32f10x_tim.o
    0x08001992   0x08001992   0x00000002   PAD
    0x08001994   0x08001994   0x000000a0   Code   RO         2472    i.TIM_OC3Init       stm32f10x_tim.o
    0x08001a34   0x08001a34   0x00000012   Code   RO         2475    i.TIM_OC3PreloadConfig  stm32f10x_tim.o
    0x08001a46   0x08001a46   0x00000002   PAD
    0x08001a48   0x08001a48   0x0000007c   Code   RO         2477    i.TIM_OC4Init       stm32f10x_tim.o
    0x08001ac4   0x08001ac4   0x0000001a   Code   RO         2479    i.TIM_OC4PreloadConfig  stm32f10x_tim.o
    0x08001ade   0x08001ade   0x00000004   Code   RO         2494    i.TIM_SetCompare1   stm32f10x_tim.o
    0x08001ae2   0x08001ae2   0x00000004   Code   RO         2495    i.TIM_SetCompare2   stm32f10x_tim.o
    0x08001ae6   0x08001ae6   0x00000004   Code   RO         2496    i.TIM_SetCompare3   stm32f10x_tim.o
    0x08001aea   0x08001aea   0x00000006   Code   RO         2497    i.TIM_SetCompare4   stm32f10x_tim.o
    0x08001af0   0x08001af0   0x000000a4   Code   RO         2504    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08001b94   0x08001b94   0x00000078   Code   RO         3626    i.USART1_IRQHandler  usart1.o
    0x08001c0c   0x08001c0c   0x000000a0   Code   RO         3627    i.USART1_Init       usart1.o
    0x08001cac   0x08001cac   0x0000001e   Code   RO         2969    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x08001cca   0x08001cca   0x00000018   Code   RO         2972    i.USART_Cmd         stm32f10x_usart.o
    0x08001ce2   0x08001ce2   0x00000054   Code   RO         2976    i.USART_GetITStatus  stm32f10x_usart.o
    0x08001d36   0x08001d36   0x0000004a   Code   RO         2978    i.USART_ITConfig    stm32f10x_usart.o
    0x08001d80   0x08001d80   0x000000d8   Code   RO         2979    i.USART_Init        stm32f10x_usart.o
    0x08001e58   0x08001e58   0x0000000a   Code   RO         2986    i.USART_ReceiveData  stm32f10x_usart.o
    0x08001e62   0x08001e62   0x00000004   Code   RO         3398    i.UsageFault_Handler  stm32f10x_it.o
    0x08001e66   0x08001e66   0x00000002   PAD
    0x08001e68   0x08001e68   0x0000003c   Code   RO         3280    i.main              main.o
    0x08001ea4   0x08001ea4   0x0000009c   Code   RO         3630    i.parse_received_data  usart1.o
    0x08001f40   0x08001f40   0x0000002c   Code   RO         4798    locale$$code        c_w.l(lc_ctype_c.o)
    0x08001f6c   0x08001f6c   0x000000c4   Code   RO         4507    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x08002030   0x08002030   0x00000018   Code   RO         4653    x$fpl$fcmpinf       fz_ws.l(fcmpi.o)
    0x08002048   0x08002048   0x00000184   Code   RO         4514    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x080021cc   0x080021cc   0x0000003e   Code   RO         4517    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x0800220a   0x0800220a   0x00000002   PAD
    0x0800220c   0x0800220c   0x00000030   Code   RO         4522    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x0800223c   0x0800223c   0x00000026   Code   RO         4521    x$fpl$ffltu         fz_ws.l(fflt_clz.o)
    0x08002262   0x08002262   0x00000002   PAD
    0x08002264   0x08002264   0x00000068   Code   RO         4527    x$fpl$fleqf         fz_ws.l(fleqf.o)
    0x080022cc   0x080022cc   0x00000102   Code   RO         4529    x$fpl$fmul          fz_ws.l(fmul.o)
    0x080023ce   0x080023ce   0x0000008c   Code   RO         4655    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x0800245a   0x0800245a   0x0000000a   Code   RO         4657    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08002464   0x08002464   0x00000062   Code   RO         4531    x$fpl$frleqf        fz_ws.l(frleqf.o)
    0x080024c6   0x080024c6   0x00000002   PAD
    0x080024c8   0x080024c8   0x000000ea   Code   RO         4509    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x080025b2   0x080025b2   0x00000000   Code   RO         4667    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x080025b2   0x080025b2   0x00000002   PAD
    0x080025b4   0x080025b4   0x0000003c   Data   RO         4186    .constdata          servo.o
    0x080025f0   0x080025f0   0x00000020   Data   RO         4925    Region$$Table       anon$$obj.o
    0x08002610   0x08002610   0x00000110   Data   RO         4797    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08002720, Size: 0x000007f8, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08002720   0x00000014   Data   RW         1817    .data               stm32f10x_rcc.o
    0x20000014   0x08002734   0x00000014   Data   RW         3212    .data               system_stm32f10x.o
    0x20000028   0x08002748   0x00000002   Data   RW         3543    .data               pwm1.o
    0x2000002a   0x0800274a   0x00000002   Data   RW         3590    .data               pwm2.o
    0x2000002c   0x0800274c   0x0000001a   Data   RW         3632    .data               usart1.o
    0x20000046   0x08002766   0x00000002   PAD
    0x20000048   0x08002768   0x00000010   Data   RW         3996    .data               encoder.o
    0x20000058   0x08002778   0x00000008   Data   RW         4392    .data               gimbal.o
    0x20000060        -       0x00000100   Zero   RW         3631    .bss                usart1.o
    0x20000160        -       0x00000010   Zero   RW         3995    .bss                encoder.o
    0x20000170        -       0x00000028   Zero   RW         4263    .bss                motor_control.o
    0x20000198        -       0x00000060   Zero   RW         4742    .bss                c_w.l(libspace.o)
    0x200001f8        -       0x00000200   Zero   RW         3273    HEAP                startup_stm32f10x_hd.o
    0x200003f8        -       0x00000400   Zero   RW         3272    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0         32   core_cm3.o
       502         60          0         16         16      11986   encoder.o
       576         36          0          8          0       5105   gimbal.o
        84          8          0          0          0     219982   main.o
       112         12          0          0          0       2468   misc.o
        68         10          0          0         40       2574   motor_control.o
       152          0          0          0          0       1793   pid.o
       140         14          0          2          0        783   pwm1.o
       508         24          0          2          0       2660   pwm2.o
       742         74         60          0          0       6244   servo.o
        64         26        304          0       1536        756   startup_stm32f10x_hd.o
         0          0          0          0          0     207536   stm32f10x_adc.o
         0          0          0          0          0     211732   stm32f10x_dma.o
        52         12          0          0          0        980   stm32f10x_exti.o
       304          0          0          0          0      11564   stm32f10x_gpio.o
        26          0          0          0          0      32330   stm32f10x_it.o
       276         32          0         20          0      12758   stm32f10x_rcc.o
       964         92          0          0          0      30299   stm32f10x_tim.o
       438          6          0          0          0      11056   stm32f10x_usart.o
       328         28          0         20          0       1945   system_stm32f10x.o
        32          6          0          0          0        438   timer7.o
       436         86          0         26        256       2610   usart1.o

    ----------------------------------------------------------------------
      5822        <USER>        <GROUP>         96       1848     777631   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        18          0          0          2          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        60          8          0          0          0         84   __0sscanf.o
         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
       884          6          0          0          0        100   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
        64          0          0          0          0         84   _sgetc.o
        12          0          0          0          0         72   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
         2          0          0          0          0          0   libinit.o
        20          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        44          8          0          0          0         84   scanf_char.o
       128          0          0          0          0         68   strcmpv7m.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
       430          8          0          0          0        168   faddsub_clz.o
        24          0          0          0          0         68   fcmpi.o
       388         76          0          0          0         96   fdiv.o
        62          4          0          0          0         84   ffixu.o
        86          0          0          0          0        136   fflt_clz.o
       104          4          0          0          0         84   fleqf.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
        98          0          0          0          0         68   frleqf.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      3526        <USER>        <GROUP>          0         96       2396   Library Totals
        16          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1910         56        272          0         96       1456   c_w.l
      1600        100          0          0          0        940   fz_ws.l

    ----------------------------------------------------------------------
      3526        <USER>        <GROUP>          0         96       2396   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      9348        682        668         96       1944     774563   Grand Totals
      9348        682        668         96       1944     774563   ELF Image Totals
      9348        682        668         96          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                10016 (   9.78kB)
    Total RW  Size (RW Data + ZI Data)              2040 (   1.99kB)
    Total ROM Size (Code + RO Data + RW Data)      10112 (   9.88kB)

==============================================================================

