Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    stm32f10x_adc.o(.text.ADC_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_adc.o(.ARM.exidx.text.ADC_DeInit) refers to stm32f10x_adc.o(.text.ADC_DeInit) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_Init) refers to stm32f10x_adc.o(.text.ADC_Init) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_StructInit) refers to stm32f10x_adc.o(.text.ADC_StructInit) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_Cmd) refers to stm32f10x_adc.o(.text.ADC_Cmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_DMACmd) refers to stm32f10x_adc.o(.text.ADC_DMACmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ITConfig) refers to stm32f10x_adc.o(.text.ADC_ITConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ResetCalibration) refers to stm32f10x_adc.o(.text.ADC_ResetCalibration) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetResetCalibrationStatus) refers to stm32f10x_adc.o(.text.ADC_GetResetCalibrationStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_StartCalibration) refers to stm32f10x_adc.o(.text.ADC_StartCalibration) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetCalibrationStatus) refers to stm32f10x_adc.o(.text.ADC_GetCalibrationStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_SoftwareStartConvCmd) refers to stm32f10x_adc.o(.text.ADC_SoftwareStartConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetSoftwareStartConvStatus) refers to stm32f10x_adc.o(.text.ADC_GetSoftwareStartConvStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_DiscModeChannelCountConfig) refers to stm32f10x_adc.o(.text.ADC_DiscModeChannelCountConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_DiscModeCmd) refers to stm32f10x_adc.o(.text.ADC_DiscModeCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_RegularChannelConfig) refers to stm32f10x_adc.o(.text.ADC_RegularChannelConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigConvCmd) refers to stm32f10x_adc.o(.text.ADC_ExternalTrigConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetConversionValue) refers to stm32f10x_adc.o(.text.ADC_GetConversionValue) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetDualModeConversionValue) refers to stm32f10x_adc.o(.text.ADC_GetDualModeConversionValue) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_AutoInjectedConvCmd) refers to stm32f10x_adc.o(.text.ADC_AutoInjectedConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedDiscModeCmd) refers to stm32f10x_adc.o(.text.ADC_InjectedDiscModeCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigInjectedConvConfig) refers to stm32f10x_adc.o(.text.ADC_ExternalTrigInjectedConvConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigInjectedConvCmd) refers to stm32f10x_adc.o(.text.ADC_ExternalTrigInjectedConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_SoftwareStartInjectedConvCmd) refers to stm32f10x_adc.o(.text.ADC_SoftwareStartInjectedConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetSoftwareStartInjectedConvCmdStatus) refers to stm32f10x_adc.o(.text.ADC_GetSoftwareStartInjectedConvCmdStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedChannelConfig) refers to stm32f10x_adc.o(.text.ADC_InjectedChannelConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedSequencerLengthConfig) refers to stm32f10x_adc.o(.text.ADC_InjectedSequencerLengthConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_SetInjectedOffset) refers to stm32f10x_adc.o(.text.ADC_SetInjectedOffset) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetInjectedConversionValue) refers to stm32f10x_adc.o(.text.ADC_GetInjectedConversionValue) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogCmd) refers to stm32f10x_adc.o(.text.ADC_AnalogWatchdogCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogThresholdsConfig) refers to stm32f10x_adc.o(.text.ADC_AnalogWatchdogThresholdsConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogSingleChannelConfig) refers to stm32f10x_adc.o(.text.ADC_AnalogWatchdogSingleChannelConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_TempSensorVrefintCmd) refers to stm32f10x_adc.o(.text.ADC_TempSensorVrefintCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetFlagStatus) refers to stm32f10x_adc.o(.text.ADC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ClearFlag) refers to stm32f10x_adc.o(.text.ADC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetITStatus) refers to stm32f10x_adc.o(.text.ADC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ClearITPendingBit) refers to stm32f10x_adc.o(.text.ADC_ClearITPendingBit) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.NVIC_PriorityGroupConfig) refers to misc.o(.text.NVIC_PriorityGroupConfig) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.NVIC_Init) refers to misc.o(.text.NVIC_Init) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.NVIC_SetVectorTable) refers to misc.o(.text.NVIC_SetVectorTable) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.NVIC_SystemLPConfig) refers to misc.o(.text.NVIC_SystemLPConfig) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.SysTick_CLKSourceConfig) refers to misc.o(.text.SysTick_CLKSourceConfig) for [Anonymous Symbol]
    stm32f10x_bkp.o(.text.BKP_DeInit) refers to stm32f10x_rcc.o(.text.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_DeInit) refers to stm32f10x_bkp.o(.text.BKP_DeInit) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_TamperPinLevelConfig) refers to stm32f10x_bkp.o(.text.BKP_TamperPinLevelConfig) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_TamperPinCmd) refers to stm32f10x_bkp.o(.text.BKP_TamperPinCmd) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_ITConfig) refers to stm32f10x_bkp.o(.text.BKP_ITConfig) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_RTCOutputConfig) refers to stm32f10x_bkp.o(.text.BKP_RTCOutputConfig) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_SetRTCCalibrationValue) refers to stm32f10x_bkp.o(.text.BKP_SetRTCCalibrationValue) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_WriteBackupRegister) refers to stm32f10x_bkp.o(.text.BKP_WriteBackupRegister) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_ReadBackupRegister) refers to stm32f10x_bkp.o(.text.BKP_ReadBackupRegister) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_GetFlagStatus) refers to stm32f10x_bkp.o(.text.BKP_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_ClearFlag) refers to stm32f10x_bkp.o(.text.BKP_ClearFlag) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_GetITStatus) refers to stm32f10x_bkp.o(.text.BKP_GetITStatus) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_ClearITPendingBit) refers to stm32f10x_bkp.o(.text.BKP_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_can.o(.text.CAN_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(.ARM.exidx.text.CAN_DeInit) refers to stm32f10x_can.o(.text.CAN_DeInit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_Init) refers to stm32f10x_can.o(.text.CAN_Init) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_FilterInit) refers to stm32f10x_can.o(.text.CAN_FilterInit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_StructInit) refers to stm32f10x_can.o(.text.CAN_StructInit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_SlaveStartBank) refers to stm32f10x_can.o(.text.CAN_SlaveStartBank) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_DBGFreeze) refers to stm32f10x_can.o(.text.CAN_DBGFreeze) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_TTComModeCmd) refers to stm32f10x_can.o(.text.CAN_TTComModeCmd) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_Transmit) refers to stm32f10x_can.o(.text.CAN_Transmit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_TransmitStatus) refers to stm32f10x_can.o(.text.CAN_TransmitStatus) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_CancelTransmit) refers to stm32f10x_can.o(.text.CAN_CancelTransmit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_Receive) refers to stm32f10x_can.o(.text.CAN_Receive) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_FIFORelease) refers to stm32f10x_can.o(.text.CAN_FIFORelease) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_MessagePending) refers to stm32f10x_can.o(.text.CAN_MessagePending) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_OperatingModeRequest) refers to stm32f10x_can.o(.text.CAN_OperatingModeRequest) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_Sleep) refers to stm32f10x_can.o(.text.CAN_Sleep) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_WakeUp) refers to stm32f10x_can.o(.text.CAN_WakeUp) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetLastErrorCode) refers to stm32f10x_can.o(.text.CAN_GetLastErrorCode) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetReceiveErrorCounter) refers to stm32f10x_can.o(.text.CAN_GetReceiveErrorCounter) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetLSBTransmitErrorCounter) refers to stm32f10x_can.o(.text.CAN_GetLSBTransmitErrorCounter) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_ITConfig) refers to stm32f10x_can.o(.text.CAN_ITConfig) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetFlagStatus) refers to stm32f10x_can.o(.text.CAN_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_ClearFlag) refers to stm32f10x_can.o(.text.CAN_ClearFlag) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetITStatus) refers to stm32f10x_can.o(.text.CAN_GetITStatus) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_ClearITPendingBit) refers to stm32f10x_can.o(.text.CAN_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_cec.o(.text.CEC_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_cec.o(.ARM.exidx.text.CEC_DeInit) refers to stm32f10x_cec.o(.text.CEC_DeInit) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_Init) refers to stm32f10x_cec.o(.text.CEC_Init) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_Cmd) refers to stm32f10x_cec.o(.text.CEC_Cmd) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_ITConfig) refers to stm32f10x_cec.o(.text.CEC_ITConfig) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_OwnAddressConfig) refers to stm32f10x_cec.o(.text.CEC_OwnAddressConfig) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_SetPrescaler) refers to stm32f10x_cec.o(.text.CEC_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_SendDataByte) refers to stm32f10x_cec.o(.text.CEC_SendDataByte) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_ReceiveDataByte) refers to stm32f10x_cec.o(.text.CEC_ReceiveDataByte) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_StartOfMessage) refers to stm32f10x_cec.o(.text.CEC_StartOfMessage) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_EndOfMessageCmd) refers to stm32f10x_cec.o(.text.CEC_EndOfMessageCmd) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_GetFlagStatus) refers to stm32f10x_cec.o(.text.CEC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_ClearFlag) refers to stm32f10x_cec.o(.text.CEC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_GetITStatus) refers to stm32f10x_cec.o(.text.CEC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_ClearITPendingBit) refers to stm32f10x_cec.o(.text.CEC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_ResetDR) refers to stm32f10x_crc.o(.text.CRC_ResetDR) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_CalcCRC) refers to stm32f10x_crc.o(.text.CRC_CalcCRC) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_CalcBlockCRC) refers to stm32f10x_crc.o(.text.CRC_CalcBlockCRC) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_GetCRC) refers to stm32f10x_crc.o(.text.CRC_GetCRC) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_SetIDRegister) refers to stm32f10x_crc.o(.text.CRC_SetIDRegister) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_GetIDRegister) refers to stm32f10x_crc.o(.text.CRC_GetIDRegister) for [Anonymous Symbol]
    stm32f10x_dac.o(.text.DAC_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(.ARM.exidx.text.DAC_DeInit) refers to stm32f10x_dac.o(.text.DAC_DeInit) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_Init) refers to stm32f10x_dac.o(.text.DAC_Init) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_StructInit) refers to stm32f10x_dac.o(.text.DAC_StructInit) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_Cmd) refers to stm32f10x_dac.o(.text.DAC_Cmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_DMACmd) refers to stm32f10x_dac.o(.text.DAC_DMACmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_SoftwareTriggerCmd) refers to stm32f10x_dac.o(.text.DAC_SoftwareTriggerCmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_DualSoftwareTriggerCmd) refers to stm32f10x_dac.o(.text.DAC_DualSoftwareTriggerCmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_WaveGenerationCmd) refers to stm32f10x_dac.o(.text.DAC_WaveGenerationCmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_SetChannel1Data) refers to stm32f10x_dac.o(.text.DAC_SetChannel1Data) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_SetChannel2Data) refers to stm32f10x_dac.o(.text.DAC_SetChannel2Data) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_SetDualChannelData) refers to stm32f10x_dac.o(.text.DAC_SetDualChannelData) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_GetDataOutputValue) refers to stm32f10x_dac.o(.text.DAC_GetDataOutputValue) for [Anonymous Symbol]
    stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_GetREVID) refers to stm32f10x_dbgmcu.o(.text.DBGMCU_GetREVID) for [Anonymous Symbol]
    stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_GetDEVID) refers to stm32f10x_dbgmcu.o(.text.DBGMCU_GetDEVID) for [Anonymous Symbol]
    stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_Config) refers to stm32f10x_dbgmcu.o(.text.DBGMCU_Config) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_DeInit) refers to stm32f10x_dma.o(.text.DMA_DeInit) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_Init) refers to stm32f10x_dma.o(.text.DMA_Init) for [Anonymous Symbol]
    stm32f10x_dma.o(.text.DMA_StructInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32f10x_dma.o(.ARM.exidx.text.DMA_StructInit) refers to stm32f10x_dma.o(.text.DMA_StructInit) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_Cmd) refers to stm32f10x_dma.o(.text.DMA_Cmd) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_ITConfig) refers to stm32f10x_dma.o(.text.DMA_ITConfig) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_SetCurrDataCounter) refers to stm32f10x_dma.o(.text.DMA_SetCurrDataCounter) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_GetCurrDataCounter) refers to stm32f10x_dma.o(.text.DMA_GetCurrDataCounter) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_GetFlagStatus) refers to stm32f10x_dma.o(.text.DMA_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_ClearFlag) refers to stm32f10x_dma.o(.text.DMA_ClearFlag) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_GetITStatus) refers to stm32f10x_dma.o(.text.DMA_GetITStatus) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_ClearITPendingBit) refers to stm32f10x_dma.o(.text.DMA_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_DeInit) refers to stm32f10x_exti.o(.text.EXTI_DeInit) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_Init) refers to stm32f10x_exti.o(.text.EXTI_Init) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_StructInit) refers to stm32f10x_exti.o(.text.EXTI_StructInit) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_GenerateSWInterrupt) refers to stm32f10x_exti.o(.text.EXTI_GenerateSWInterrupt) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_GetFlagStatus) refers to stm32f10x_exti.o(.text.EXTI_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_ClearFlag) refers to stm32f10x_exti.o(.text.EXTI_ClearFlag) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_GetITStatus) refers to stm32f10x_exti.o(.text.EXTI_GetITStatus) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_ClearITPendingBit) refers to stm32f10x_exti.o(.text.EXTI_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_SetLatency) refers to stm32f10x_flash.o(.text.FLASH_SetLatency) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_HalfCycleAccessCmd) refers to stm32f10x_flash.o(.text.FLASH_HalfCycleAccessCmd) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_PrefetchBufferCmd) refers to stm32f10x_flash.o(.text.FLASH_PrefetchBufferCmd) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_Unlock) refers to stm32f10x_flash.o(.text.FLASH_Unlock) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_UnlockBank1) refers to stm32f10x_flash.o(.text.FLASH_UnlockBank1) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_Lock) refers to stm32f10x_flash.o(.text.FLASH_Lock) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_LockBank1) refers to stm32f10x_flash.o(.text.FLASH_LockBank1) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ErasePage) refers to stm32f10x_flash.o(.text.FLASH_ErasePage) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseAllPages) refers to stm32f10x_flash.o(.text.FLASH_EraseAllPages) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(.text.FLASH_EraseAllBank1Pages) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(.text.FLASH_WaitForLastBank1Operation) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(.text.FLASH_EraseOptionBytes) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetReadOutProtectionStatus) refers to stm32f10x_flash.o(.text.FLASH_GetReadOutProtectionStatus) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramWord) refers to stm32f10x_flash.o(.text.FLASH_ProgramWord) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(.text.FLASH_ProgramHalfWord) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(.text.FLASH_ProgramOptionByteData) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(.text.FLASH_EnableWriteProtection) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(.text.FLASH_ReadOutProtection) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(.text.FLASH_UserOptionByteConfig) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetUserOptionByte) refers to stm32f10x_flash.o(.text.FLASH_GetUserOptionByte) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetWriteProtectionOptionByte) refers to stm32f10x_flash.o(.text.FLASH_GetWriteProtectionOptionByte) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetPrefetchBufferStatus) refers to stm32f10x_flash.o(.text.FLASH_GetPrefetchBufferStatus) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ITConfig) refers to stm32f10x_flash.o(.text.FLASH_ITConfig) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetFlagStatus) refers to stm32f10x_flash.o(.text.FLASH_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ClearFlag) refers to stm32f10x_flash.o(.text.FLASH_ClearFlag) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetStatus) refers to stm32f10x_flash.o(.text.FLASH_GetStatus) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetBank1Status) refers to stm32f10x_flash.o(.text.FLASH_GetBank1Status) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMDeInit) refers to stm32f10x_fsmc.o(.text.FSMC_NORSRAMDeInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDDeInit) refers to stm32f10x_fsmc.o(.text.FSMC_NANDDeInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDDeInit) refers to stm32f10x_fsmc.o(.text.FSMC_PCCARDDeInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMInit) refers to stm32f10x_fsmc.o(.text.FSMC_NORSRAMInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDInit) refers to stm32f10x_fsmc.o(.text.FSMC_NANDInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDInit) refers to stm32f10x_fsmc.o(.text.FSMC_PCCARDInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMStructInit) refers to stm32f10x_fsmc.o(.text.FSMC_NORSRAMStructInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDStructInit) refers to stm32f10x_fsmc.o(.text.FSMC_NANDStructInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDStructInit) refers to stm32f10x_fsmc.o(.text.FSMC_PCCARDStructInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMCmd) refers to stm32f10x_fsmc.o(.text.FSMC_NORSRAMCmd) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDCmd) refers to stm32f10x_fsmc.o(.text.FSMC_NANDCmd) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDCmd) refers to stm32f10x_fsmc.o(.text.FSMC_PCCARDCmd) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDECCCmd) refers to stm32f10x_fsmc.o(.text.FSMC_NANDECCCmd) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetECC) refers to stm32f10x_fsmc.o(.text.FSMC_GetECC) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ITConfig) refers to stm32f10x_fsmc.o(.text.FSMC_ITConfig) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetFlagStatus) refers to stm32f10x_fsmc.o(.text.FSMC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ClearFlag) refers to stm32f10x_fsmc.o(.text.FSMC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetITStatus) refers to stm32f10x_fsmc.o(.text.FSMC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ClearITPendingBit) refers to stm32f10x_fsmc.o(.text.FSMC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.text.GPIO_DeInit) refers to stm32f10x_gpio.o(.rodata..Lswitch.table.GPIO_DeInit.1) for .Lswitch.table.GPIO_DeInit.1
    stm32f10x_gpio.o(.text.GPIO_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_DeInit) refers to stm32f10x_gpio.o(.text.GPIO_DeInit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.text.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_AFIODeInit) refers to stm32f10x_gpio.o(.text.GPIO_AFIODeInit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_StructInit) refers to stm32f10x_gpio.o(.text.GPIO_StructInit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadInputDataBit) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadInputData) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputData) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadOutputDataBit) refers to stm32f10x_gpio.o(.text.GPIO_ReadOutputDataBit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadOutputData) refers to stm32f10x_gpio.o(.text.GPIO_ReadOutputData) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_SetBits) refers to stm32f10x_gpio.o(.text.GPIO_SetBits) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ResetBits) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_WriteBit) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_Write) refers to stm32f10x_gpio.o(.text.GPIO_Write) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_PinLockConfig) refers to stm32f10x_gpio.o(.text.GPIO_PinLockConfig) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EventOutputConfig) refers to stm32f10x_gpio.o(.text.GPIO_EventOutputConfig) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EventOutputCmd) refers to stm32f10x_gpio.o(.text.GPIO_EventOutputCmd) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_PinRemapConfig) refers to stm32f10x_gpio.o(.text.GPIO_PinRemapConfig) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EXTILineConfig) refers to stm32f10x_gpio.o(.text.GPIO_EXTILineConfig) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ETH_MediaInterfaceConfig) refers to stm32f10x_gpio.o(.text.GPIO_ETH_MediaInterfaceConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.text.I2C_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_DeInit) refers to stm32f10x_i2c.o(.text.I2C_DeInit) for [Anonymous Symbol]
    stm32f10x_i2c.o(.text.I2C_Init) refers to stm32f10x_rcc.o(.text.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_Init) refers to stm32f10x_i2c.o(.text.I2C_Init) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_StructInit) refers to stm32f10x_i2c.o(.text.I2C_StructInit) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_Cmd) refers to stm32f10x_i2c.o(.text.I2C_Cmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_DMACmd) refers to stm32f10x_i2c.o(.text.I2C_DMACmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_DMALastTransferCmd) refers to stm32f10x_i2c.o(.text.I2C_DMALastTransferCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GenerateSTART) refers to stm32f10x_i2c.o(.text.I2C_GenerateSTART) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GenerateSTOP) refers to stm32f10x_i2c.o(.text.I2C_GenerateSTOP) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_AcknowledgeConfig) refers to stm32f10x_i2c.o(.text.I2C_AcknowledgeConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_OwnAddress2Config) refers to stm32f10x_i2c.o(.text.I2C_OwnAddress2Config) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_DualAddressCmd) refers to stm32f10x_i2c.o(.text.I2C_DualAddressCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GeneralCallCmd) refers to stm32f10x_i2c.o(.text.I2C_GeneralCallCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ITConfig) refers to stm32f10x_i2c.o(.text.I2C_ITConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_SendData) refers to stm32f10x_i2c.o(.text.I2C_SendData) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ReceiveData) refers to stm32f10x_i2c.o(.text.I2C_ReceiveData) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_Send7bitAddress) refers to stm32f10x_i2c.o(.text.I2C_Send7bitAddress) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ReadRegister) refers to stm32f10x_i2c.o(.text.I2C_ReadRegister) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_SoftwareResetCmd) refers to stm32f10x_i2c.o(.text.I2C_SoftwareResetCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_NACKPositionConfig) refers to stm32f10x_i2c.o(.text.I2C_NACKPositionConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_SMBusAlertConfig) refers to stm32f10x_i2c.o(.text.I2C_SMBusAlertConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_TransmitPEC) refers to stm32f10x_i2c.o(.text.I2C_TransmitPEC) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_PECPositionConfig) refers to stm32f10x_i2c.o(.text.I2C_PECPositionConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_CalculatePEC) refers to stm32f10x_i2c.o(.text.I2C_CalculatePEC) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetPEC) refers to stm32f10x_i2c.o(.text.I2C_GetPEC) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ARPCmd) refers to stm32f10x_i2c.o(.text.I2C_ARPCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_StretchClockCmd) refers to stm32f10x_i2c.o(.text.I2C_StretchClockCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_FastModeDutyCycleConfig) refers to stm32f10x_i2c.o(.text.I2C_FastModeDutyCycleConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_CheckEvent) refers to stm32f10x_i2c.o(.text.I2C_CheckEvent) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetLastEvent) refers to stm32f10x_i2c.o(.text.I2C_GetLastEvent) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetFlagStatus) refers to stm32f10x_i2c.o(.text.I2C_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ClearFlag) refers to stm32f10x_i2c.o(.text.I2C_ClearFlag) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetITStatus) refers to stm32f10x_i2c.o(.text.I2C_GetITStatus) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ClearITPendingBit) refers to stm32f10x_i2c.o(.text.I2C_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_WriteAccessCmd) refers to stm32f10x_iwdg.o(.text.IWDG_WriteAccessCmd) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_SetPrescaler) refers to stm32f10x_iwdg.o(.text.IWDG_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_SetReload) refers to stm32f10x_iwdg.o(.text.IWDG_SetReload) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_ReloadCounter) refers to stm32f10x_iwdg.o(.text.IWDG_ReloadCounter) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_Enable) refers to stm32f10x_iwdg.o(.text.IWDG_Enable) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_GetFlagStatus) refers to stm32f10x_iwdg.o(.text.IWDG_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_pwr.o(.text.PWR_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_DeInit) refers to stm32f10x_pwr.o(.text.PWR_DeInit) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_BackupAccessCmd) refers to stm32f10x_pwr.o(.text.PWR_BackupAccessCmd) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_PVDCmd) refers to stm32f10x_pwr.o(.text.PWR_PVDCmd) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_PVDLevelConfig) refers to stm32f10x_pwr.o(.text.PWR_PVDLevelConfig) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_WakeUpPinCmd) refers to stm32f10x_pwr.o(.text.PWR_WakeUpPinCmd) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_EnterSTOPMode) refers to stm32f10x_pwr.o(.text.PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_EnterSTANDBYMode) refers to stm32f10x_pwr.o(.text.PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_GetFlagStatus) refers to stm32f10x_pwr.o(.text.PWR_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_ClearFlag) refers to stm32f10x_pwr.o(.text.PWR_ClearFlag) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_DeInit) refers to stm32f10x_rcc.o(.text.RCC_DeInit) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_HSEConfig) refers to stm32f10x_rcc.o(.text.RCC_HSEConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(.text.RCC_WaitForHSEStartUp) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetFlagStatus) refers to stm32f10x_rcc.o(.text.RCC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_AdjustHSICalibrationValue) refers to stm32f10x_rcc.o(.text.RCC_AdjustHSICalibrationValue) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_HSICmd) refers to stm32f10x_rcc.o(.text.RCC_HSICmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_PLLConfig) refers to stm32f10x_rcc.o(.text.RCC_PLLConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_PLLCmd) refers to stm32f10x_rcc.o(.text.RCC_PLLCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_SYSCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_SYSCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetSYSCLKSource) refers to stm32f10x_rcc.o(.text.RCC_GetSYSCLKSource) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_HCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_HCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_PCLK1Config) refers to stm32f10x_rcc.o(.text.RCC_PCLK1Config) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_PCLK2Config) refers to stm32f10x_rcc.o(.text.RCC_PCLK2Config) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ITConfig) refers to stm32f10x_rcc.o(.text.RCC_ITConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_USBCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_USBCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ADCCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_ADCCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_LSEConfig) refers to stm32f10x_rcc.o(.text.RCC_LSEConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_LSICmd) refers to stm32f10x_rcc.o(.text.RCC_LSICmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_RTCCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_RTCCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_RTCCLKCmd) refers to stm32f10x_rcc.o(.text.RCC_RTCCLKCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.text.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.rodata.APBAHBPrescTable) for APBAHBPrescTable
    stm32f10x_rcc.o(.text.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.rodata.ADCPrescTable) for ADCPrescTable
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.text.RCC_GetClocksFreq) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_AHBPeriphClockCmd) refers to stm32f10x_rcc.o(.text.RCC_AHBPeriphClockCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB2PeriphClockCmd) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB1PeriphClockCmd) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB2PeriphResetCmd) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB1PeriphResetCmd) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_BackupResetCmd) refers to stm32f10x_rcc.o(.text.RCC_BackupResetCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClockSecuritySystemCmd) refers to stm32f10x_rcc.o(.text.RCC_ClockSecuritySystemCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_MCOConfig) refers to stm32f10x_rcc.o(.text.RCC_MCOConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClearFlag) refers to stm32f10x_rcc.o(.text.RCC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetITStatus) refers to stm32f10x_rcc.o(.text.RCC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClearITPendingBit) refers to stm32f10x_rcc.o(.text.RCC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_ITConfig) refers to stm32f10x_rtc.o(.text.RTC_ITConfig) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_EnterConfigMode) refers to stm32f10x_rtc.o(.text.RTC_EnterConfigMode) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_ExitConfigMode) refers to stm32f10x_rtc.o(.text.RTC_ExitConfigMode) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetCounter) refers to stm32f10x_rtc.o(.text.RTC_GetCounter) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetCounter) refers to stm32f10x_rtc.o(.text.RTC_SetCounter) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetPrescaler) refers to stm32f10x_rtc.o(.text.RTC_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetAlarm) refers to stm32f10x_rtc.o(.text.RTC_SetAlarm) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetDivider) refers to stm32f10x_rtc.o(.text.RTC_GetDivider) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_WaitForLastTask) refers to stm32f10x_rtc.o(.text.RTC_WaitForLastTask) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_WaitForSynchro) refers to stm32f10x_rtc.o(.text.RTC_WaitForSynchro) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetFlagStatus) refers to stm32f10x_rtc.o(.text.RTC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_ClearFlag) refers to stm32f10x_rtc.o(.text.RTC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetITStatus) refers to stm32f10x_rtc.o(.text.RTC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_ClearITPendingBit) refers to stm32f10x_rtc.o(.text.RTC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DeInit) refers to stm32f10x_sdio.o(.text.SDIO_DeInit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_Init) refers to stm32f10x_sdio.o(.text.SDIO_Init) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StructInit) refers to stm32f10x_sdio.o(.text.SDIO_StructInit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClockCmd) refers to stm32f10x_sdio.o(.text.SDIO_ClockCmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetPowerState) refers to stm32f10x_sdio.o(.text.SDIO_SetPowerState) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetPowerState) refers to stm32f10x_sdio.o(.text.SDIO_GetPowerState) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ITConfig) refers to stm32f10x_sdio.o(.text.SDIO_ITConfig) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DMACmd) refers to stm32f10x_sdio.o(.text.SDIO_DMACmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendCommand) refers to stm32f10x_sdio.o(.text.SDIO_SendCommand) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CmdStructInit) refers to stm32f10x_sdio.o(.text.SDIO_CmdStructInit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetCommandResponse) refers to stm32f10x_sdio.o(.text.SDIO_GetCommandResponse) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetResponse) refers to stm32f10x_sdio.o(.text.SDIO_GetResponse) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DataConfig) refers to stm32f10x_sdio.o(.text.SDIO_DataConfig) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DataStructInit) refers to stm32f10x_sdio.o(.text.SDIO_DataStructInit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetDataCounter) refers to stm32f10x_sdio.o(.text.SDIO_GetDataCounter) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ReadData) refers to stm32f10x_sdio.o(.text.SDIO_ReadData) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_WriteData) refers to stm32f10x_sdio.o(.text.SDIO_WriteData) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetFIFOCount) refers to stm32f10x_sdio.o(.text.SDIO_GetFIFOCount) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StartSDIOReadWait) refers to stm32f10x_sdio.o(.text.SDIO_StartSDIOReadWait) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StopSDIOReadWait) refers to stm32f10x_sdio.o(.text.SDIO_StopSDIOReadWait) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetSDIOReadWaitMode) refers to stm32f10x_sdio.o(.text.SDIO_SetSDIOReadWaitMode) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetSDIOOperation) refers to stm32f10x_sdio.o(.text.SDIO_SetSDIOOperation) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendSDIOSuspendCmd) refers to stm32f10x_sdio.o(.text.SDIO_SendSDIOSuspendCmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CommandCompletionCmd) refers to stm32f10x_sdio.o(.text.SDIO_CommandCompletionCmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CEATAITCmd) refers to stm32f10x_sdio.o(.text.SDIO_CEATAITCmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendCEATACmd) refers to stm32f10x_sdio.o(.text.SDIO_SendCEATACmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetFlagStatus) refers to stm32f10x_sdio.o(.text.SDIO_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClearFlag) refers to stm32f10x_sdio.o(.text.SDIO_ClearFlag) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetITStatus) refers to stm32f10x_sdio.o(.text.SDIO_GetITStatus) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClearITPendingBit) refers to stm32f10x_sdio.o(.text.SDIO_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_spi.o(.text.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(.text.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_DeInit) refers to stm32f10x_spi.o(.text.SPI_I2S_DeInit) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_Init) refers to stm32f10x_spi.o(.text.SPI_Init) for [Anonymous Symbol]
    stm32f10x_spi.o(.text.I2S_Init) refers to stm32f10x_rcc.o(.text.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(.ARM.exidx.text.I2S_Init) refers to stm32f10x_spi.o(.text.I2S_Init) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_StructInit) refers to stm32f10x_spi.o(.text.SPI_StructInit) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.I2S_StructInit) refers to stm32f10x_spi.o(.text.I2S_StructInit) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_Cmd) refers to stm32f10x_spi.o(.text.SPI_Cmd) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.I2S_Cmd) refers to stm32f10x_spi.o(.text.I2S_Cmd) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ITConfig) refers to stm32f10x_spi.o(.text.SPI_I2S_ITConfig) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_DMACmd) refers to stm32f10x_spi.o(.text.SPI_I2S_DMACmd) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_SendData) refers to stm32f10x_spi.o(.text.SPI_I2S_SendData) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ReceiveData) refers to stm32f10x_spi.o(.text.SPI_I2S_ReceiveData) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_NSSInternalSoftwareConfig) refers to stm32f10x_spi.o(.text.SPI_NSSInternalSoftwareConfig) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_SSOutputCmd) refers to stm32f10x_spi.o(.text.SPI_SSOutputCmd) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_DataSizeConfig) refers to stm32f10x_spi.o(.text.SPI_DataSizeConfig) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_TransmitCRC) refers to stm32f10x_spi.o(.text.SPI_TransmitCRC) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_CalculateCRC) refers to stm32f10x_spi.o(.text.SPI_CalculateCRC) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_GetCRC) refers to stm32f10x_spi.o(.text.SPI_GetCRC) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_GetCRCPolynomial) refers to stm32f10x_spi.o(.text.SPI_GetCRCPolynomial) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_BiDirectionalLineConfig) refers to stm32f10x_spi.o(.text.SPI_BiDirectionalLineConfig) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_GetFlagStatus) refers to stm32f10x_spi.o(.text.SPI_I2S_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ClearFlag) refers to stm32f10x_spi.o(.text.SPI_I2S_ClearFlag) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_GetITStatus) refers to stm32f10x_spi.o(.text.SPI_I2S_GetITStatus) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ClearITPendingBit) refers to stm32f10x_spi.o(.text.SPI_I2S_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_tim.o(.text.TIM_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(.text.TIM_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(.ARM.exidx.text.TIM_DeInit) refers to stm32f10x_tim.o(.text.TIM_DeInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_TimeBaseInit) refers to stm32f10x_tim.o(.text.TIM_TimeBaseInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1Init) refers to stm32f10x_tim.o(.text.TIM_OC1Init) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2Init) refers to stm32f10x_tim.o(.text.TIM_OC2Init) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3Init) refers to stm32f10x_tim.o(.text.TIM_OC3Init) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4Init) refers to stm32f10x_tim.o(.text.TIM_OC4Init) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ICInit) refers to stm32f10x_tim.o(.text.TIM_ICInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC1Prescaler) refers to stm32f10x_tim.o(.text.TIM_SetIC1Prescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC2Prescaler) refers to stm32f10x_tim.o(.text.TIM_SetIC2Prescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC3Prescaler) refers to stm32f10x_tim.o(.text.TIM_SetIC3Prescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC4Prescaler) refers to stm32f10x_tim.o(.text.TIM_SetIC4Prescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_PWMIConfig) refers to stm32f10x_tim.o(.text.TIM_PWMIConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_BDTRConfig) refers to stm32f10x_tim.o(.text.TIM_BDTRConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_TimeBaseStructInit) refers to stm32f10x_tim.o(.text.TIM_TimeBaseStructInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OCStructInit) refers to stm32f10x_tim.o(.text.TIM_OCStructInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ICStructInit) refers to stm32f10x_tim.o(.text.TIM_ICStructInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_BDTRStructInit) refers to stm32f10x_tim.o(.text.TIM_BDTRStructInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_Cmd) refers to stm32f10x_tim.o(.text.TIM_Cmd) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CtrlPWMOutputs) refers to stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ITConfig) refers to stm32f10x_tim.o(.text.TIM_ITConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GenerateEvent) refers to stm32f10x_tim.o(.text.TIM_GenerateEvent) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_DMAConfig) refers to stm32f10x_tim.o(.text.TIM_DMAConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_DMACmd) refers to stm32f10x_tim.o(.text.TIM_DMACmd) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_InternalClockConfig) refers to stm32f10x_tim.o(.text.TIM_InternalClockConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(.text.TIM_ITRxExternalClockConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectInputTrigger) refers to stm32f10x_tim.o(.text.TIM_SelectInputTrigger) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(.text.TIM_TIxExternalClockConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(.text.TIM_ETRClockMode1Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRConfig) refers to stm32f10x_tim.o(.text.TIM_ETRConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(.text.TIM_ETRClockMode2Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_PrescalerConfig) refers to stm32f10x_tim.o(.text.TIM_PrescalerConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CounterModeConfig) refers to stm32f10x_tim.o(.text.TIM_CounterModeConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_EncoderInterfaceConfig) refers to stm32f10x_tim.o(.text.TIM_EncoderInterfaceConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC1Config) refers to stm32f10x_tim.o(.text.TIM_ForcedOC1Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC2Config) refers to stm32f10x_tim.o(.text.TIM_ForcedOC2Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC3Config) refers to stm32f10x_tim.o(.text.TIM_ForcedOC3Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC4Config) refers to stm32f10x_tim.o(.text.TIM_ForcedOC4Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ARRPreloadConfig) refers to stm32f10x_tim.o(.text.TIM_ARRPreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectCOM) refers to stm32f10x_tim.o(.text.TIM_SelectCOM) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectCCDMA) refers to stm32f10x_tim.o(.text.TIM_SelectCCDMA) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CCPreloadControl) refers to stm32f10x_tim.o(.text.TIM_CCPreloadControl) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1PreloadConfig) refers to stm32f10x_tim.o(.text.TIM_OC1PreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2PreloadConfig) refers to stm32f10x_tim.o(.text.TIM_OC2PreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3PreloadConfig) refers to stm32f10x_tim.o(.text.TIM_OC3PreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4PreloadConfig) refers to stm32f10x_tim.o(.text.TIM_OC4PreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1FastConfig) refers to stm32f10x_tim.o(.text.TIM_OC1FastConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2FastConfig) refers to stm32f10x_tim.o(.text.TIM_OC2FastConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3FastConfig) refers to stm32f10x_tim.o(.text.TIM_OC3FastConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4FastConfig) refers to stm32f10x_tim.o(.text.TIM_OC4FastConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC1Ref) refers to stm32f10x_tim.o(.text.TIM_ClearOC1Ref) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC2Ref) refers to stm32f10x_tim.o(.text.TIM_ClearOC2Ref) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC3Ref) refers to stm32f10x_tim.o(.text.TIM_ClearOC3Ref) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC4Ref) refers to stm32f10x_tim.o(.text.TIM_ClearOC4Ref) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1PolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC1PolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1NPolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC1NPolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2PolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC2PolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2NPolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC2NPolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3PolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC3PolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3NPolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC3NPolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4PolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC4PolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CCxCmd) refers to stm32f10x_tim.o(.text.TIM_CCxCmd) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CCxNCmd) refers to stm32f10x_tim.o(.text.TIM_CCxNCmd) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOCxM) refers to stm32f10x_tim.o(.text.TIM_SelectOCxM) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_UpdateDisableConfig) refers to stm32f10x_tim.o(.text.TIM_UpdateDisableConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_UpdateRequestConfig) refers to stm32f10x_tim.o(.text.TIM_UpdateRequestConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectHallSensor) refers to stm32f10x_tim.o(.text.TIM_SelectHallSensor) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOnePulseMode) refers to stm32f10x_tim.o(.text.TIM_SelectOnePulseMode) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOutputTrigger) refers to stm32f10x_tim.o(.text.TIM_SelectOutputTrigger) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectSlaveMode) refers to stm32f10x_tim.o(.text.TIM_SelectSlaveMode) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectMasterSlaveMode) refers to stm32f10x_tim.o(.text.TIM_SelectMasterSlaveMode) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCounter) refers to stm32f10x_tim.o(.text.TIM_SetCounter) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetAutoreload) refers to stm32f10x_tim.o(.text.TIM_SetAutoreload) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare1) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare2) refers to stm32f10x_tim.o(.text.TIM_SetCompare2) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare3) refers to stm32f10x_tim.o(.text.TIM_SetCompare3) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare4) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetClockDivision) refers to stm32f10x_tim.o(.text.TIM_SetClockDivision) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture1) refers to stm32f10x_tim.o(.text.TIM_GetCapture1) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture2) refers to stm32f10x_tim.o(.text.TIM_GetCapture2) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture3) refers to stm32f10x_tim.o(.text.TIM_GetCapture3) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture4) refers to stm32f10x_tim.o(.text.TIM_GetCapture4) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCounter) refers to stm32f10x_tim.o(.text.TIM_GetCounter) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetPrescaler) refers to stm32f10x_tim.o(.text.TIM_GetPrescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetFlagStatus) refers to stm32f10x_tim.o(.text.TIM_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearFlag) refers to stm32f10x_tim.o(.text.TIM_ClearFlag) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetITStatus) refers to stm32f10x_tim.o(.text.TIM_GetITStatus) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearITPendingBit) refers to stm32f10x_tim.o(.text.TIM_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_usart.o(.text.USART_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(.text.USART_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(.ARM.exidx.text.USART_DeInit) refers to stm32f10x_usart.o(.text.USART_DeInit) for [Anonymous Symbol]
    stm32f10x_usart.o(.text.USART_Init) refers to stm32f10x_rcc.o(.text.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_usart.o(.ARM.exidx.text.USART_Init) refers to stm32f10x_usart.o(.text.USART_Init) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_StructInit) refers to stm32f10x_usart.o(.text.USART_StructInit) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ClockInit) refers to stm32f10x_usart.o(.text.USART_ClockInit) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ClockStructInit) refers to stm32f10x_usart.o(.text.USART_ClockStructInit) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_Cmd) refers to stm32f10x_usart.o(.text.USART_Cmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ITConfig) refers to stm32f10x_usart.o(.text.USART_ITConfig) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_DMACmd) refers to stm32f10x_usart.o(.text.USART_DMACmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SetAddress) refers to stm32f10x_usart.o(.text.USART_SetAddress) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_WakeUpConfig) refers to stm32f10x_usart.o(.text.USART_WakeUpConfig) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ReceiverWakeUpCmd) refers to stm32f10x_usart.o(.text.USART_ReceiverWakeUpCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_LINBreakDetectLengthConfig) refers to stm32f10x_usart.o(.text.USART_LINBreakDetectLengthConfig) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_LINCmd) refers to stm32f10x_usart.o(.text.USART_LINCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SendData) refers to stm32f10x_usart.o(.text.USART_SendData) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ReceiveData) refers to stm32f10x_usart.o(.text.USART_ReceiveData) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SendBreak) refers to stm32f10x_usart.o(.text.USART_SendBreak) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SetGuardTime) refers to stm32f10x_usart.o(.text.USART_SetGuardTime) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SetPrescaler) refers to stm32f10x_usart.o(.text.USART_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SmartCardCmd) refers to stm32f10x_usart.o(.text.USART_SmartCardCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SmartCardNACKCmd) refers to stm32f10x_usart.o(.text.USART_SmartCardNACKCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_HalfDuplexCmd) refers to stm32f10x_usart.o(.text.USART_HalfDuplexCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_OverSampling8Cmd) refers to stm32f10x_usart.o(.text.USART_OverSampling8Cmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_OneBitMethodCmd) refers to stm32f10x_usart.o(.text.USART_OneBitMethodCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_IrDAConfig) refers to stm32f10x_usart.o(.text.USART_IrDAConfig) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_IrDACmd) refers to stm32f10x_usart.o(.text.USART_IrDACmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_GetFlagStatus) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ClearFlag) refers to stm32f10x_usart.o(.text.USART_ClearFlag) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_GetITStatus) refers to stm32f10x_usart.o(.text.USART_GetITStatus) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ClearITPendingBit) refers to stm32f10x_usart.o(.text.USART_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.text.WWDG_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_DeInit) refers to stm32f10x_wwdg.o(.text.WWDG_DeInit) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetPrescaler) refers to stm32f10x_wwdg.o(.text.WWDG_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetWindowValue) refers to stm32f10x_wwdg.o(.text.WWDG_SetWindowValue) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_EnableIT) refers to stm32f10x_wwdg.o(.text.WWDG_EnableIT) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetCounter) refers to stm32f10x_wwdg.o(.text.WWDG_SetCounter) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_Enable) refers to stm32f10x_wwdg.o(.text.WWDG_Enable) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_GetFlagStatus) refers to stm32f10x_wwdg.o(.text.WWDG_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_ClearFlag) refers to stm32f10x_wwdg.o(.text.WWDG_ClearFlag) for [Anonymous Symbol]
    system_stm32f10x.o(.ARM.exidx.text.SystemInit) refers to system_stm32f10x.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f10x.o(.text.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f10x.o(.text.SystemCoreClockUpdate) refers to system_stm32f10x.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f10x.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f10x.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_PSP) refers to core_cm3.o(.text.__get_PSP) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_PSP) refers to core_cm3.o(.text.__set_PSP) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_MSP) refers to core_cm3.o(.text.__get_MSP) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_MSP) refers to core_cm3.o(.text.__set_MSP) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_BASEPRI) refers to core_cm3.o(.text.__get_BASEPRI) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_BASEPRI) refers to core_cm3.o(.text.__set_BASEPRI) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_PRIMASK) refers to core_cm3.o(.text.__get_PRIMASK) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_PRIMASK) refers to core_cm3.o(.text.__set_PRIMASK) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_FAULTMASK) refers to core_cm3.o(.text.__get_FAULTMASK) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_FAULTMASK) refers to core_cm3.o(.text.__set_FAULTMASK) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_CONTROL) refers to core_cm3.o(.text.__get_CONTROL) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_CONTROL) refers to core_cm3.o(.text.__set_CONTROL) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__REV) refers to core_cm3.o(.text.__REV) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__REV16) refers to core_cm3.o(.text.__REV16) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__REVSH) refers to core_cm3.o(.text.__REVSH) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__RBIT) refers to core_cm3.o(.text.__RBIT) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__LDREXB) refers to core_cm3.o(.text.__LDREXB) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__LDREXH) refers to core_cm3.o(.text.__LDREXH) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__LDREXW) refers to core_cm3.o(.text.__LDREXW) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__STREXB) refers to core_cm3.o(.text.__STREXB) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__STREXH) refers to core_cm3.o(.text.__STREXH) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__STREXW) refers to core_cm3.o(.text.__STREXW) for [Anonymous Symbol]
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to encoder.o(.text.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to encoder.o(.text.EXTI1_IRQHandler) for EXTI1_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to encoder.o(.text.EXTI2_IRQHandler) for EXTI2_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to encoder.o(.text.TIM6_IRQHandler) for TIM6_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to timer7.o(.text.TIM7_IRQHandler) for TIM7_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    main.o(.ARM.exidx.text.Delay_ms) refers to main.o(.text.Delay_ms) for [Anonymous Symbol]
    main.o(.text.main) refers to system_stm32f10x.o(.text.SystemInit) for SystemInit
    main.o(.text.main) refers to gimbal.o(.text.Gimbal_Init) for Gimbal_Init
    main.o(.text.main) refers to usart1.o(.text.USART1_Init) for USART1_Init
    main.o(.text.main) refers to gimbal.o(.text.Gimbal_AdjustToCenterTarget) for Gimbal_AdjustToCenterTarget
    main.o(.text.main) refers to gimbal.o(.text.Gimbal_UpdatePosition) for Gimbal_UpdatePosition
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f10x_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f10x_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f10x_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f10x_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f10x_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f10x_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f10x_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f10x_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f10x_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.text.USART1_IRQHandler) refers to stm32f10x_usart.o(.text.USART_GetITStatus) for USART_GetITStatus
    stm32f10x_it.o(.text.USART1_IRQHandler) refers to stm32f10x_usart.o(.text.USART_ReceiveData) for USART_ReceiveData
    stm32f10x_it.o(.text.USART1_IRQHandler) refers to usart1.o(.text.USART1_ProcessReceivedData) for USART1_ProcessReceivedData
    stm32f10x_it.o(.text.USART1_IRQHandler) refers to stm32f10x_usart.o(.text.USART_ClearITPendingBit) for USART_ClearITPendingBit
    stm32f10x_it.o(.ARM.exidx.text.USART1_IRQHandler) refers to stm32f10x_it.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    pwm1.o(.text.PWM_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pwm1.o(.text.PWM_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    pwm1.o(.text.PWM_Init) refers to system_stm32f10x.o(.data.SystemCoreClock) for SystemCoreClock
    pwm1.o(.text.PWM_Init) refers to pwm1.o(.bss.pwmPeriod) for pwmPeriod
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_TimeBaseInit) for TIM_TimeBaseInit
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC3Init) for TIM_OC3Init
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC1Init) for TIM_OC1Init
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC4Init) for TIM_OC4Init
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC2Init) for TIM_OC2Init
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm1.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    pwm1.o(.ARM.exidx.text.PWM_Init) refers to pwm1.o(.text.PWM_Init) for [Anonymous Symbol]
    pwm1.o(.text.PWM_SetDutyCycle) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmplt
    pwm1.o(.text.PWM_SetDutyCycle) refers to pwm1.o(.bss.pwmPeriod) for pwmPeriod
    pwm1.o(.text.PWM_SetDutyCycle) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    pwm1.o(.text.PWM_SetDutyCycle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pwm1.o(.text.PWM_SetDutyCycle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    pwm1.o(.text.PWM_SetDutyCycle) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    pwm1.o(.text.PWM_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for TIM_SetCompare1
    pwm1.o(.text.PWM_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare3) for TIM_SetCompare3
    pwm1.o(.text.PWM_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for TIM_SetCompare4
    pwm1.o(.text.PWM_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare2) for TIM_SetCompare2
    pwm1.o(.ARM.exidx.text.PWM_SetDutyCycle) refers to pwm1.o(.text.PWM_SetDutyCycle) for [Anonymous Symbol]
    pwm1.o(.text.PWM_Start) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm1.o(.text.PWM_Start) refers to stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    pwm1.o(.ARM.exidx.text.PWM_Start) refers to pwm1.o(.text.PWM_Start) for [Anonymous Symbol]
    pwm1.o(.text.PWM_Stop) refers to stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    pwm1.o(.text.PWM_Stop) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm1.o(.ARM.exidx.text.PWM_Stop) refers to pwm1.o(.text.PWM_Stop) for [Anonymous Symbol]
    pwm1.o(.text.PWM_SetDeadTime) refers to stm32f10x_tim.o(.text.TIM_BDTRStructInit) for TIM_BDTRStructInit
    pwm1.o(.text.PWM_SetDeadTime) refers to stm32f10x_tim.o(.text.TIM_BDTRConfig) for TIM_BDTRConfig
    pwm1.o(.ARM.exidx.text.PWM_SetDeadTime) refers to pwm1.o(.text.PWM_SetDeadTime) for [Anonymous Symbol]
    pwm1.o(.text.PwmConfig) refers to pwm1.o(.text.PWM_Init) for PWM_Init
    pwm1.o(.text.PwmConfig) refers to pwm1.o(.bss.pwmPeriod) for pwmPeriod
    pwm1.o(.text.PwmConfig) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    pwm1.o(.text.PwmConfig) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pwm1.o(.text.PwmConfig) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    pwm1.o(.text.PwmConfig) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    pwm1.o(.text.PwmConfig) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for TIM_SetCompare1
    pwm1.o(.text.PwmConfig) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm1.o(.text.PwmConfig) refers to stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    pwm1.o(.ARM.exidx.text.PwmConfig) refers to pwm1.o(.text.PwmConfig) for [Anonymous Symbol]
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    pwm2.o(.text.PWM2_Init) refers to system_stm32f10x.o(.data.SystemCoreClock) for SystemCoreClock
    pwm2.o(.text.PWM2_Init) refers to pwm2.o(.bss.pwmPeriod) for pwmPeriod
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_TimeBaseInit) for TIM_TimeBaseInit
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_OC3Init) for TIM_OC3Init
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_OC1Init) for TIM_OC1Init
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_OC4Init) for TIM_OC4Init
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_OC2Init) for TIM_OC2Init
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    pwm2.o(.text.PWM2_Init) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm2.o(.ARM.exidx.text.PWM2_Init) refers to pwm2.o(.text.PWM2_Init) for [Anonymous Symbol]
    pwm2.o(.text.PWM2_SetDutyCycle) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmplt
    pwm2.o(.text.PWM2_SetDutyCycle) refers to pwm2.o(.bss.pwmPeriod) for pwmPeriod
    pwm2.o(.text.PWM2_SetDutyCycle) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    pwm2.o(.text.PWM2_SetDutyCycle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pwm2.o(.text.PWM2_SetDutyCycle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    pwm2.o(.text.PWM2_SetDutyCycle) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    pwm2.o(.text.PWM2_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for TIM_SetCompare1
    pwm2.o(.text.PWM2_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare3) for TIM_SetCompare3
    pwm2.o(.text.PWM2_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for TIM_SetCompare4
    pwm2.o(.text.PWM2_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare2) for TIM_SetCompare2
    pwm2.o(.ARM.exidx.text.PWM2_SetDutyCycle) refers to pwm2.o(.text.PWM2_SetDutyCycle) for [Anonymous Symbol]
    pwm2.o(.text.PWM2_Start) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm2.o(.ARM.exidx.text.PWM2_Start) refers to pwm2.o(.text.PWM2_Start) for [Anonymous Symbol]
    pwm2.o(.text.PWM2_Stop) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm2.o(.ARM.exidx.text.PWM2_Stop) refers to pwm2.o(.text.PWM2_Stop) for [Anonymous Symbol]
    pwm2.o(.text.Pwm2Config_Channel4) refers to pwm2.o(.text.PWM2_Init) for PWM2_Init
    pwm2.o(.text.Pwm2Config_Channel4) refers to pwm2.o(.bss.pwmPeriod) for pwmPeriod
    pwm2.o(.text.Pwm2Config_Channel4) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    pwm2.o(.text.Pwm2Config_Channel4) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pwm2.o(.text.Pwm2Config_Channel4) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    pwm2.o(.text.Pwm2Config_Channel4) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    pwm2.o(.text.Pwm2Config_Channel4) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for TIM_SetCompare4
    pwm2.o(.text.Pwm2Config_Channel4) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm2.o(.ARM.exidx.text.Pwm2Config_Channel4) refers to pwm2.o(.text.Pwm2Config_Channel4) for [Anonymous Symbol]
    usart1.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.text) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.text.parse_received_data) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.text.parse_received_data) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.text.parse_received_data) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.text.parse_received_data) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.text.parse_received_data) refers to usart1.o(.data.redx) for redx
    usart1.o(.text.parse_received_data) refers to usart1.o(.data.redy) for redy
    usart1.o(.text.parse_received_data) refers to usart1.o(.rodata.str1.1) for .L.str
    usart1.o(.text.parse_received_data) refers to __0sscanf.o(.text) for __0sscanf
    usart1.o(.text.parse_received_data) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for USART_GetFlagStatus
    usart1.o(.text.parse_received_data) refers to stm32f10x_usart.o(.text.USART_SendData) for USART_SendData
    usart1.o(.text.parse_received_data) refers to __2sprintf.o(.text) for __2sprintf
    usart1.o(.text.parse_received_data) refers to usart1.o(.bss.vertex_x) for vertex_x
    usart1.o(.text.parse_received_data) refers to usart1.o(.bss.vertex_y) for vertex_y
    usart1.o(.text.parse_received_data) refers to usart1.o(.bss.centerx) for centerx
    usart1.o(.text.parse_received_data) refers to usart1.o(.bss.centery) for centery
    usart1.o(.ARM.exidx.text.parse_received_data) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.ARM.exidx.text.parse_received_data) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.ARM.exidx.text.parse_received_data) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.ARM.exidx.text.parse_received_data) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.ARM.exidx.text.parse_received_data) refers to usart1.o(.text.parse_received_data) for [Anonymous Symbol]
    usart1.o(.text.USART1_SendString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.text.USART1_SendString) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.text.USART1_SendString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.text.USART1_SendString) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.text.USART1_SendString) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for USART_GetFlagStatus
    usart1.o(.text.USART1_SendString) refers to stm32f10x_usart.o(.text.USART_SendData) for USART_SendData
    usart1.o(.ARM.exidx.text.USART1_SendString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.ARM.exidx.text.USART1_SendString) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.ARM.exidx.text.USART1_SendString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.ARM.exidx.text.USART1_SendString) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.ARM.exidx.text.USART1_SendString) refers to usart1.o(.text.USART1_SendString) for [Anonymous Symbol]
    usart1.o(.text.USART1_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.text.USART1_Init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.text.USART1_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.text.USART1_Init) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.text.USART1_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart1.o(.text.USART1_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    usart1.o(.text.USART1_Init) refers to stm32f10x_usart.o(.text.USART_Init) for USART_Init
    usart1.o(.text.USART1_Init) refers to stm32f10x_usart.o(.text.USART_ITConfig) for USART_ITConfig
    usart1.o(.text.USART1_Init) refers to misc.o(.text.NVIC_Init) for NVIC_Init
    usart1.o(.text.USART1_Init) refers to stm32f10x_usart.o(.text.USART_Cmd) for USART_Cmd
    usart1.o(.ARM.exidx.text.USART1_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.ARM.exidx.text.USART1_Init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.ARM.exidx.text.USART1_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.ARM.exidx.text.USART1_Init) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.ARM.exidx.text.USART1_Init) refers to usart1.o(.text.USART1_Init) for [Anonymous Symbol]
    usart1.o(.text.USART1_SendChar) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.text.USART1_SendChar) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.text.USART1_SendChar) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.text.USART1_SendChar) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.text.USART1_SendChar) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for USART_GetFlagStatus
    usart1.o(.text.USART1_SendChar) refers to stm32f10x_usart.o(.text.USART_SendData) for USART_SendData
    usart1.o(.ARM.exidx.text.USART1_SendChar) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.ARM.exidx.text.USART1_SendChar) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.ARM.exidx.text.USART1_SendChar) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.ARM.exidx.text.USART1_SendChar) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.ARM.exidx.text.USART1_SendChar) refers to usart1.o(.text.USART1_SendChar) for [Anonymous Symbol]
    usart1.o(.text.USART1_ProcessReceivedData) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.text.USART1_ProcessReceivedData) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.text.USART1_ProcessReceivedData) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.text.USART1_ProcessReceivedData) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.text.USART1_ProcessReceivedData) refers to usart1.o(.bss.rx_index) for rx_index
    usart1.o(.text.USART1_ProcessReceivedData) refers to usart1.o(.bss.rx_buffer) for rx_buffer
    usart1.o(.text.USART1_ProcessReceivedData) refers to usart1.o(.text.parse_received_data) for parse_received_data
    usart1.o(.ARM.exidx.text.USART1_ProcessReceivedData) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.ARM.exidx.text.USART1_ProcessReceivedData) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.ARM.exidx.text.USART1_ProcessReceivedData) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.ARM.exidx.text.USART1_ProcessReceivedData) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.ARM.exidx.text.USART1_ProcessReceivedData) refers to usart1.o(.text.USART1_ProcessReceivedData) for [Anonymous Symbol]
    usart1.o(.data.redx) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.data.redx) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.data.redx) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.data.redx) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.data.redy) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.data.redy) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.data.redy) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.data.redy) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.bss.centerx) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.bss.centerx) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.bss.centerx) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.bss.centerx) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.bss.centery) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.bss.centery) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.bss.centery) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.bss.centery) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.bss.vertex_x) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.bss.vertex_x) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.bss.vertex_x) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.bss.vertex_x) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.bss.vertex_y) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.bss.vertex_y) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.bss.vertex_y) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.bss.vertex_y) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.rodata.str1.1) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.rodata.str1.1) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.rodata.str1.1) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.rodata.str1.1) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.bss.rx_index) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.bss.rx_index) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.bss.rx_index) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.bss.rx_index) refers (Special) to _scanf_int.o(.text) for _scanf_int
    usart1.o(.bss.rx_buffer) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart1.o(.bss.rx_buffer) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart1.o(.bss.rx_buffer) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart1.o(.bss.rx_buffer) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_vision.o(.text.UART_Vision_Init) refers to uart_vision.o(.bss.vision_display_buffer) for vision_display_buffer
    uart_vision.o(.text.UART_Vision_Init) refers to uart_vision.o(.bss.rx_buffer) for rx_buffer
    uart_vision.o(.text.UART_Vision_Init) refers to uart_vision.o(.bss.rx_index) for rx_index
    uart_vision.o(.ARM.exidx.text.UART_Vision_Init) refers to uart_vision.o(.text.UART_Vision_Init) for [Anonymous Symbol]
    uart_vision.o(.text.UART_Vision_ProcessData) refers to uart_vision.o(.bss.rx_index) for rx_index
    uart_vision.o(.text.UART_Vision_ProcessData) refers to uart_vision.o(.bss.rx_buffer) for rx_buffer
    uart_vision.o(.text.UART_Vision_ProcessData) refers to uart_vision.o(.bss.vision_display_buffer) for vision_display_buffer
    uart_vision.o(.text.UART_Vision_ProcessData) refers to strcpy.o(.text) for strcpy
    uart_vision.o(.ARM.exidx.text.UART_Vision_ProcessData) refers to uart_vision.o(.text.UART_Vision_ProcessData) for [Anonymous Symbol]
    timer7.o(.text.TIM7_Config) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer7.o(.text.TIM7_Config) refers to stm32f10x_tim.o(.text.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer7.o(.text.TIM7_Config) refers to stm32f10x_tim.o(.text.TIM_ITConfig) for TIM_ITConfig
    timer7.o(.text.TIM7_Config) refers to misc.o(.text.NVIC_Init) for NVIC_Init
    timer7.o(.text.TIM7_Config) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    timer7.o(.ARM.exidx.text.TIM7_Config) refers to timer7.o(.text.TIM7_Config) for [Anonymous Symbol]
    timer7.o(.text.TIM7_IRQHandler) refers to stm32f10x_tim.o(.text.TIM_GetITStatus) for TIM_GetITStatus
    timer7.o(.text.TIM7_IRQHandler) refers to stm32f10x_tim.o(.text.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    timer7.o(.text.TIM7_IRQHandler) refers to motor_control.o(.text.MotorControl_Update) for MotorControl_Update
    timer7.o(.ARM.exidx.text.TIM7_IRQHandler) refers to timer7.o(.text.TIM7_IRQHandler) for [Anonymous Symbol]
    oled.o(.text.OLED_W_SCL) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_W_SCL) refers to oled.o(.text.OLED_W_SCL) for [Anonymous Symbol]
    oled.o(.text.OLED_W_SDA) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_W_SDA) refers to oled.o(.text.OLED_W_SDA) for [Anonymous Symbol]
    oled.o(.text.OLED_GPIO_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(.text.OLED_GPIO_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    oled.o(.text.OLED_GPIO_Init) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_GPIO_Init) refers to oled.o(.text.OLED_GPIO_Init) for [Anonymous Symbol]
    oled.o(.text.OLED_I2C_Start) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_I2C_Start) refers to oled.o(.text.OLED_I2C_Start) for [Anonymous Symbol]
    oled.o(.text.OLED_I2C_Stop) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_I2C_Stop) refers to oled.o(.text.OLED_I2C_Stop) for [Anonymous Symbol]
    oled.o(.text.OLED_I2C_SendByte) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_I2C_SendByte) refers to oled.o(.text.OLED_I2C_SendByte) for [Anonymous Symbol]
    oled.o(.text.OLED_WriteCommand) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.OLED_WriteCommand) refers to oled.o(.text.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(.ARM.exidx.text.OLED_WriteCommand) refers to oled.o(.text.OLED_WriteCommand) for [Anonymous Symbol]
    oled.o(.text.OLED_WriteData) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.OLED_WriteData) refers to oled.o(.text.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(.ARM.exidx.text.OLED_WriteData) refers to oled.o(.text.OLED_WriteData) for [Anonymous Symbol]
    oled.o(.text.OLED_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(.text.OLED_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    oled.o(.text.OLED_Init) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(.text.OLED_Init) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_WriteData) for OLED_WriteData
    oled.o(.ARM.exidx.text.OLED_Init) refers to oled.o(.text.OLED_Init) for [Anonymous Symbol]
    oled.o(.text.OLED_Clear) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_Clear) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    oled.o(.ARM.exidx.text.OLED_Clear) refers to oled.o(.text.OLED_Clear) for [Anonymous Symbol]
    oled.o(.text.OLED_Update) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_Update) refers to oled.o(.text.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(.text.OLED_Update) refers to oled.o(.text.OLED_WriteData) for OLED_WriteData
    oled.o(.ARM.exidx.text.OLED_Update) refers to oled.o(.text.OLED_Update) for [Anonymous Symbol]
    oled.o(.text.OLED_SetCursor) refers to oled.o(.text.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(.ARM.exidx.text.OLED_SetCursor) refers to oled.o(.text.OLED_SetCursor) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_Pow) refers to oled.o(.text.OLED_Pow) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_pnpoly) refers to oled.o(.text.OLED_pnpoly) for [Anonymous Symbol]
    oled.o(.text.OLED_IsInAngle) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(.text.OLED_IsInAngle) refers to atan2.o(i.atan2) for atan2
    oled.o(.text.OLED_IsInAngle) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    oled.o(.text.OLED_IsInAngle) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(.text.OLED_IsInAngle) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    oled.o(.ARM.exidx.text.OLED_IsInAngle) refers to oled.o(.text.OLED_IsInAngle) for [Anonymous Symbol]
    oled.o(.text.OLED_UpdateArea) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_UpdateArea) refers to oled.o(.text.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(.text.OLED_UpdateArea) refers to oled.o(.text.OLED_WriteData) for OLED_WriteData
    oled.o(.ARM.exidx.text.OLED_UpdateArea) refers to oled.o(.text.OLED_UpdateArea) for [Anonymous Symbol]
    oled.o(.text.OLED_ClearArea) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_ClearArea) refers to oled.o(.text.OLED_ClearArea) for [Anonymous Symbol]
    oled.o(.text.OLED_Reverse) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_Reverse) refers to oled.o(.text.OLED_Reverse) for [Anonymous Symbol]
    oled.o(.text.OLED_ReverseArea) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_ReverseArea) refers to oled.o(.text.OLED_ReverseArea) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowChar) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowChar) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowChar) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.ARM.exidx.text.OLED_ShowChar) refers to oled.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowImage) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_ShowImage) refers to oled.o(.text.OLED_ShowImage) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowString) refers to oled_data.o(.rodata.OLED_CF16x16) for OLED_CF16x16
    oled.o(.text.OLED_ShowString) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.text.OLED_ShowString) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowString) refers to strcmpv7m.o(.text) for strcmp
    oled.o(.ARM.exidx.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowString) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowNum) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowNum) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.text.OLED_ShowNum) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.ARM.exidx.text.OLED_ShowNum) refers to oled.o(.text.OLED_ShowNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowSignedNum) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowSignedNum) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowSignedNum) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.ARM.exidx.text.OLED_ShowSignedNum) refers to oled.o(.text.OLED_ShowSignedNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowHexNum) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.text.OLED_ShowHexNum) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowHexNum) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.ARM.exidx.text.OLED_ShowHexNum) refers to oled.o(.text.OLED_ShowHexNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowBinNum) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowBinNum) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.text.OLED_ShowBinNum) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.ARM.exidx.text.OLED_ShowBinNum) refers to oled.o(.text.OLED_ShowBinNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowFloatNum) refers to dcmp.o(x$fpl$fcmp) for __aeabi_dcmplt
    oled.o(.text.OLED_ShowFloatNum) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowFloatNum) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowFloatNum) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.text.OLED_ShowFloatNum) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    oled.o(.text.OLED_ShowFloatNum) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(.text.OLED_ShowFloatNum) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    oled.o(.text.OLED_ShowFloatNum) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(.text.OLED_ShowFloatNum) refers to round.o(i.round) for round
    oled.o(.ARM.exidx.text.OLED_ShowFloatNum) refers to oled.o(.text.OLED_ShowFloatNum) for [Anonymous Symbol]
    oled.o(.text.OLED_Printf) refers to vsprintf.o(.text) for vsprintf
    oled.o(.text.OLED_Printf) refers to oled.o(.text.OLED_ShowString) for OLED_ShowString
    oled.o(.ARM.exidx.text.OLED_Printf) refers to oled.o(.text.OLED_Printf) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawPoint) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_DrawPoint) refers to oled.o(.text.OLED_DrawPoint) for [Anonymous Symbol]
    oled.o(.text.OLED_GetPoint) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_GetPoint) refers to oled.o(.text.OLED_GetPoint) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawLine) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_DrawLine) refers to oled.o(.text.OLED_DrawLine) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawRectangle) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_DrawRectangle) refers to oled.o(.text.OLED_DrawRectangle) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawTriangle) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_DrawTriangle) refers to oled.o(.text.OLED_DrawLine) for OLED_DrawLine
    oled.o(.ARM.exidx.text.OLED_DrawTriangle) refers to oled.o(.text.OLED_DrawTriangle) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawCircle) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_DrawCircle) refers to oled.o(.text.OLED_DrawCircle) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawEllipse) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(.text.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    oled.o(.text.OLED_DrawEllipse) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(.text.OLED_DrawEllipse) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    oled.o(.text.OLED_DrawEllipse) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_DrawEllipse) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(.text.OLED_DrawEllipse) refers to dcmp.o(x$fpl$fcmp) for __aeabi_dcmple
    oled.o(.text.OLED_DrawEllipse) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmpgt
    oled.o(.text.OLED_DrawEllipse) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    oled.o(.text.OLED_DrawEllipse) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    oled.o(.text.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    oled.o(.ARM.exidx.text.OLED_DrawEllipse) refers to oled.o(.text.OLED_DrawEllipse) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawArc) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(.text.OLED_DrawArc) refers to atan2.o(i.atan2) for atan2
    oled.o(.text.OLED_DrawArc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    oled.o(.text.OLED_DrawArc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(.text.OLED_DrawArc) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    oled.o(.text.OLED_DrawArc) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_DrawArc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(.ARM.exidx.text.OLED_DrawArc) refers to oled.o(.text.OLED_DrawArc) for [Anonymous Symbol]
    encoder.o(.text.EXTI0_IRQHandler) refers to stm32f10x_exti.o(.text.EXTI_GetITStatus) for EXTI_GetITStatus
    encoder.o(.text.EXTI0_IRQHandler) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(.text.EXTI0_IRQHandler) refers to encoder.o(.bss.encoder) for encoder
    encoder.o(.text.EXTI0_IRQHandler) refers to encoder.o(.data.path) for path
    encoder.o(.text.EXTI0_IRQHandler) refers to encoder.o(.bss.target_absolute_count) for target_absolute_count
    encoder.o(.text.EXTI0_IRQHandler) refers to motor_control.o(.text.MotorControl_SetSpeed) for MotorControl_SetSpeed
    encoder.o(.text.EXTI0_IRQHandler) refers to servo.o(.text.Left) for Left
    encoder.o(.text.EXTI0_IRQHandler) refers to servo.o(.text.Straight) for Straight
    encoder.o(.text.EXTI0_IRQHandler) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for GPIO_ResetBits
    encoder.o(.text.EXTI0_IRQHandler) refers to servo.o(.text.Right) for Right
    encoder.o(.text.EXTI0_IRQHandler) refers to stm32f10x_exti.o(.text.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(.ARM.exidx.text.EXTI0_IRQHandler) refers to encoder.o(.text.EXTI0_IRQHandler) for [Anonymous Symbol]
    encoder.o(.text.EXTI2_IRQHandler) refers to stm32f10x_exti.o(.text.EXTI_GetITStatus) for EXTI_GetITStatus
    encoder.o(.text.EXTI2_IRQHandler) refers to stm32f10x_tim.o(.text.TIM_GetCounter) for TIM_GetCounter
    encoder.o(.text.EXTI2_IRQHandler) refers to encoder.o(.data.EXTI2_IRQHandler.last_tim6_count) for EXTI2_IRQHandler.last_tim6_count
    encoder.o(.text.EXTI2_IRQHandler) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(.text.EXTI2_IRQHandler) refers to encoder.o(.bss.encoder) for encoder
    encoder.o(.text.EXTI2_IRQHandler) refers to stm32f10x_exti.o(.text.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(.ARM.exidx.text.EXTI2_IRQHandler) refers to encoder.o(.text.EXTI2_IRQHandler) for [Anonymous Symbol]
    encoder.o(.text.TIM6_IRQHandler) refers to stm32f10x_tim.o(.text.TIM_GetITStatus) for TIM_GetITStatus
    encoder.o(.text.TIM6_IRQHandler) refers to encoder.o(.bss.encoder) for encoder
    encoder.o(.text.TIM6_IRQHandler) refers to encoder.o(.bss.TIM6_IRQHandler.last_absolute_count) for TIM6_IRQHandler.last_absolute_count
    encoder.o(.text.TIM6_IRQHandler) refers to stm32f10x_tim.o(.text.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    encoder.o(.ARM.exidx.text.TIM6_IRQHandler) refers to encoder.o(.text.TIM6_IRQHandler) for [Anonymous Symbol]
    encoder.o(.text.Encoder_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(.text.Encoder_Init) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    encoder.o(.text.Encoder_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    encoder.o(.text.Encoder_Init) refers to stm32f10x_gpio.o(.text.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    encoder.o(.text.Encoder_Init) refers to stm32f10x_exti.o(.text.EXTI_Init) for EXTI_Init
    encoder.o(.text.Encoder_Init) refers to misc.o(.text.NVIC_Init) for NVIC_Init
    encoder.o(.text.Encoder_Init) refers to stm32f10x_tim.o(.text.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(.text.Encoder_Init) refers to stm32f10x_tim.o(.text.TIM_ITConfig) for TIM_ITConfig
    encoder.o(.text.Encoder_Init) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    encoder.o(.ARM.exidx.text.Encoder_Init) refers to encoder.o(.text.Encoder_Init) for [Anonymous Symbol]
    encoder.o(.text.Encoder_GetData) refers to encoder.o(.bss.encoder) for encoder
    encoder.o(.ARM.exidx.text.Encoder_GetData) refers to encoder.o(.text.Encoder_GetData) for [Anonymous Symbol]
    encoder.o(.text.Show_Encoder) refers to encoder.o(.bss.encoder) for encoder
    encoder.o(.text.Show_Encoder) refers to oled.o(.text.OLED_ShowSignedNum) for OLED_ShowSignedNum
    encoder.o(.text.Show_Encoder) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    encoder.o(.text.Show_Encoder) refers to oled.o(.text.OLED_ShowFloatNum) for OLED_ShowFloatNum
    encoder.o(.text.Show_Encoder) refers to oled.o(.text.OLED_Update) for OLED_Update
    encoder.o(.ARM.exidx.text.Show_Encoder) refers to encoder.o(.text.Show_Encoder) for [Anonymous Symbol]
    encoder.o(.text.EXTI1_IRQHandler) refers to stm32f10x_exti.o(.text.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(.ARM.exidx.text.EXTI1_IRQHandler) refers to encoder.o(.text.EXTI1_IRQHandler) for [Anonymous Symbol]
    encoder.o(.text.SetMotorWithRotation) refers to encoder.o(.bss.target_absolute_count) for target_absolute_count
    encoder.o(.ARM.exidx.text.SetMotorWithRotation) refers to encoder.o(.text.SetMotorWithRotation) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.PID_Init) refers to pid.o(.text.PID_Init) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.PID_SetTarget) refers to pid.o(.text.PID_SetTarget) for [Anonymous Symbol]
    pid.o(.text.PID_Update) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    pid.o(.text.PID_Update) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid.o(.text.PID_Update) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    pid.o(.text.PID_Update) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmpgt
    pid.o(.ARM.exidx.text.PID_Update) refers to pid.o(.text.PID_Update) for [Anonymous Symbol]
    servo.o(.text.Servo_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    servo.o(.text.Servo_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    servo.o(.text.Servo_Init) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for GPIO_ResetBits
    servo.o(.text.Servo_Init) refers to stm32f10x_gpio.o(.text.GPIO_SetBits) for GPIO_SetBits
    servo.o(.text.Servo_Init) refers to pwm2.o(.text.PWM2_Init) for PWM2_Init
    servo.o(.text.Servo_Init) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Servo_Init) refers to servo.o(.text.Servo_Init) for [Anonymous Symbol]
    servo.o(.text.Servo_SetAngle) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    servo.o(.text.Servo_SetAngle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(.text.Servo_SetAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(.text.Servo_SetAngle) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Servo_SetAngle) refers to servo.o(.text.Servo_SetAngle) for [Anonymous Symbol]
    servo.o(.text.Servo2_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    servo.o(.text.Servo2_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    servo.o(.text.Servo2_Init) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for GPIO_ResetBits
    servo.o(.text.Servo2_Init) refers to stm32f10x_gpio.o(.text.GPIO_SetBits) for GPIO_SetBits
    servo.o(.text.Servo2_Init) refers to pwm2.o(.text.PWM2_Init) for PWM2_Init
    servo.o(.text.Servo2_Init) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Servo2_Init) refers to servo.o(.text.Servo2_Init) for [Anonymous Symbol]
    servo.o(.text.Servo2_SetAngle) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    servo.o(.text.Servo2_SetAngle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(.text.Servo2_SetAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(.text.Servo2_SetAngle) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Servo2_SetAngle) refers to servo.o(.text.Servo2_SetAngle) for [Anonymous Symbol]
    servo.o(.text.Servo3_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    servo.o(.text.Servo3_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    servo.o(.text.Servo3_Init) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for GPIO_ResetBits
    servo.o(.text.Servo3_Init) refers to stm32f10x_gpio.o(.text.GPIO_SetBits) for GPIO_SetBits
    servo.o(.text.Servo3_Init) refers to pwm2.o(.text.PWM2_Init) for PWM2_Init
    servo.o(.text.Servo3_Init) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Servo3_Init) refers to servo.o(.text.Servo3_Init) for [Anonymous Symbol]
    servo.o(.text.Servo3_SetAngle) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    servo.o(.text.Servo3_SetAngle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(.text.Servo3_SetAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(.text.Servo3_SetAngle) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Servo3_SetAngle) refers to servo.o(.text.Servo3_SetAngle) for [Anonymous Symbol]
    servo.o(.text.Servo4_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    servo.o(.text.Servo4_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    servo.o(.text.Servo4_Init) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for GPIO_ResetBits
    servo.o(.text.Servo4_Init) refers to stm32f10x_gpio.o(.text.GPIO_SetBits) for GPIO_SetBits
    servo.o(.text.Servo4_Init) refers to pwm2.o(.text.PWM2_Init) for PWM2_Init
    servo.o(.text.Servo4_Init) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Servo4_Init) refers to servo.o(.text.Servo4_Init) for [Anonymous Symbol]
    servo.o(.text.Servo4_SetAngle) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    servo.o(.text.Servo4_SetAngle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    servo.o(.text.Servo4_SetAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    servo.o(.text.Servo4_SetAngle) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Servo4_SetAngle) refers to servo.o(.text.Servo4_SetAngle) for [Anonymous Symbol]
    servo.o(.text.Left) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Left) refers to servo.o(.text.Left) for [Anonymous Symbol]
    servo.o(.text.Right) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Right) refers to servo.o(.text.Right) for [Anonymous Symbol]
    servo.o(.text.Straight) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.Straight) refers to servo.o(.text.Straight) for [Anonymous Symbol]
    servo.o(.text.servo_update) refers to uart_vision.o(.bss.vision_display_buffer) for vision_display_buffer
    servo.o(.text.servo_update) refers to pwm2.o(.text.PWM2_SetDutyCycle) for PWM2_SetDutyCycle
    servo.o(.ARM.exidx.text.servo_update) refers to servo.o(.text.servo_update) for [Anonymous Symbol]
    motor_control.o(.text.Motor_Config) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    motor_control.o(.text.Motor_Config) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    motor_control.o(.text.Motor_Config) refers to stm32f10x_gpio.o(.text.GPIO_SetBits) for GPIO_SetBits
    motor_control.o(.text.Motor_Config) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for GPIO_ResetBits
    motor_control.o(.text.Motor_Config) refers to motor_control.o(.bss.motor) for motor
    motor_control.o(.text.Motor_Config) refers to pid.o(.text.PID_Init) for PID_Init
    motor_control.o(.text.Motor_Config) refers to pid.o(.text.PID_SetTarget) for PID_SetTarget
    motor_control.o(.ARM.exidx.text.Motor_Config) refers to motor_control.o(.text.Motor_Config) for [Anonymous Symbol]
    motor_control.o(.text.MotorControl_SetSpeed) refers to motor_control.o(.bss.motor) for motor
    motor_control.o(.text.MotorControl_SetSpeed) refers to pid.o(.text.PID_SetTarget) for PID_SetTarget
    motor_control.o(.ARM.exidx.text.MotorControl_SetSpeed) refers to motor_control.o(.text.MotorControl_SetSpeed) for [Anonymous Symbol]
    motor_control.o(.text.MotorControl_Update) refers to encoder.o(.text.Encoder_GetData) for Encoder_GetData
    motor_control.o(.text.MotorControl_Update) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    motor_control.o(.text.MotorControl_Update) refers to motor_control.o(.bss.motor) for motor
    motor_control.o(.text.MotorControl_Update) refers to pid.o(.text.PID_Update) for PID_Update
    motor_control.o(.text.MotorControl_Update) refers to pwm1.o(.text.PWM_SetDutyCycle) for PWM_SetDutyCycle
    motor_control.o(.ARM.exidx.text.MotorControl_Update) refers to motor_control.o(.text.MotorControl_Update) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_Init) refers to servo.o(.text.Servo_Init) for Servo_Init
    gimbal.o(.text.Gimbal_Init) refers to servo.o(.text.Servo2_Init) for Servo2_Init
    gimbal.o(.text.Gimbal_Init) refers to servo.o(.text.Servo3_Init) for Servo3_Init
    gimbal.o(.text.Gimbal_Init) refers to servo.o(.text.Servo4_Init) for Servo4_Init
    gimbal.o(.text.Gimbal_Init) refers to gimbal.o(.data.currentPos) for currentPos
    gimbal.o(.text.Gimbal_Init) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.text.Gimbal_Init) refers to servo.o(.text.Servo_SetAngle) for Servo_SetAngle
    gimbal.o(.text.Gimbal_Init) refers to servo.o(.text.Servo2_SetAngle) for Servo2_SetAngle
    gimbal.o(.text.Gimbal_Init) refers to servo.o(.text.Servo3_SetAngle) for Servo3_SetAngle
    gimbal.o(.text.Gimbal_Init) refers to servo.o(.text.Servo4_SetAngle) for Servo4_SetAngle
    gimbal.o(.ARM.exidx.text.Gimbal_Init) refers to gimbal.o(.text.Gimbal_Init) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_SetTargetPosition) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.ARM.exidx.text.Gimbal_SetTargetPosition) refers to gimbal.o(.text.Gimbal_SetTargetPosition) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_UpdatePosition) refers to gimbal.o(.data.currentPos) for currentPos
    gimbal.o(.text.Gimbal_UpdatePosition) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.text.Gimbal_UpdatePosition) refers to servo.o(.text.Servo_SetAngle) for Servo_SetAngle
    gimbal.o(.text.Gimbal_UpdatePosition) refers to servo.o(.text.Servo2_SetAngle) for Servo2_SetAngle
    gimbal.o(.ARM.exidx.text.Gimbal_UpdatePosition) refers to gimbal.o(.text.Gimbal_UpdatePosition) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_GetCurrentPosition) refers to gimbal.o(.data.currentPos) for currentPos
    gimbal.o(.ARM.exidx.text.Gimbal_GetCurrentPosition) refers to gimbal.o(.text.Gimbal_GetCurrentPosition) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_GetTargetPosition) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.ARM.exidx.text.Gimbal_GetTargetPosition) refers to gimbal.o(.text.Gimbal_GetTargetPosition) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_MoveXPlus) refers to gimbal.o(.data.currentPos) for currentPos
    gimbal.o(.text.Gimbal_MoveXPlus) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.ARM.exidx.text.Gimbal_MoveXPlus) refers to gimbal.o(.text.Gimbal_MoveXPlus) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_MoveXMinus) refers to gimbal.o(.data.currentPos) for currentPos
    gimbal.o(.text.Gimbal_MoveXMinus) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.ARM.exidx.text.Gimbal_MoveXMinus) refers to gimbal.o(.text.Gimbal_MoveXMinus) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_MoveYPlus) refers to gimbal.o(.data.currentPos) for currentPos
    gimbal.o(.text.Gimbal_MoveYPlus) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.ARM.exidx.text.Gimbal_MoveYPlus) refers to gimbal.o(.text.Gimbal_MoveYPlus) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_MoveYMinus) refers to gimbal.o(.data.currentPos) for currentPos
    gimbal.o(.text.Gimbal_MoveYMinus) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.ARM.exidx.text.Gimbal_MoveYMinus) refers to gimbal.o(.text.Gimbal_MoveYMinus) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_AdjustToRedTarget) refers to usart1.o(.data.redx) for redx
    gimbal.o(.text.Gimbal_AdjustToRedTarget) refers to gimbal.o(.data.currentPos) for currentPos
    gimbal.o(.text.Gimbal_AdjustToRedTarget) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.text.Gimbal_AdjustToRedTarget) refers to usart1.o(.data.redy) for redy
    gimbal.o(.ARM.exidx.text.Gimbal_AdjustToRedTarget) refers to gimbal.o(.text.Gimbal_AdjustToRedTarget) for [Anonymous Symbol]
    gimbal.o(.text.Gimbal_AdjustToCenterTarget) refers to usart1.o(.bss.centerx) for centerx
    gimbal.o(.text.Gimbal_AdjustToCenterTarget) refers to usart1.o(.bss.centery) for centery
    gimbal.o(.text.Gimbal_AdjustToCenterTarget) refers to gimbal.o(.data.targetPos) for targetPos
    gimbal.o(.ARM.exidx.text.Gimbal_AdjustToCenterTarget) refers to gimbal.o(.text.Gimbal_AdjustToCenterTarget) for [Anonymous Symbol]
    vsprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dcmp.o(x$fpl$fcmp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(x$fpl$fcmp) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    dcmp.o(x$fpl$fcmp) refers to dgeqf.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(x$fpl$fcmp) refers to dleqf.o(x$fpl$dleqf) for _dcmple
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fcmp.o(x$fpl$fcmp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(x$fpl$fcmp) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(x$fpl$fcmp) refers to fgeqf.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(x$fpl$fcmp) refers to fleqf.o(x$fpl$fleqf) for _fcmple
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.atan2) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2.o(i.atan2) refers to _rserrno.o(.text) for __set_errno
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    round.o(i.round) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    round.o(i.round) refers to drnd.o(x$fpl$drnd) for _drnd
    round.o(i.round) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    round.o(i.round) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    round.o(i.round) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _c16rtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace.o(.text) for isspace
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dgeqf.o(x$fpl$dgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dgeqf.o(x$fpl$dgeqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dgeqf.o(x$fpl$dgeqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    drnd.o(x$fpl$drnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    feqf.o(x$fpl$feqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fgeqf.o(x$fpl$fgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fgeqf.o(x$fpl$fgeqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fgeqf.o(x$fpl$fgeqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.atan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec_accurate.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec_accurate.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec_accurate.o(.text) refers to btod_accurate.o(.text) for _btod_main
    _printf_fp_dec_accurate.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec_accurate.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    _c16rtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod_accurate.o(.text) refers to btod_accurate_common.o(.text) for _btod_common
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    btod_accurate_common.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    libinit2.o(.ARM.Collect$$libinit$$00000011) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing stm32f10x_adc.o(.text), (0 bytes).
    Removing stm32f10x_adc.o(.text.ADC_DeInit), (76 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_DeInit), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_Init), (82 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_Init), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_StructInit), (16 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_StructInit), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_Cmd), (16 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_Cmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_DMACmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_DMACmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ITConfig), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ITConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ResetCalibration), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetResetCalibrationStatus), (8 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetResetCalibrationStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_StartCalibration), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetCalibrationStatus), (8 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetCalibrationStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_SoftwareStartConvCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_SoftwareStartConvCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetSoftwareStartConvStatus), (8 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetSoftwareStartConvStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_DiscModeChannelCountConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_DiscModeCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_DiscModeCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_RegularChannelConfig), (96 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_RegularChannelConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ExternalTrigConvCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigConvCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetConversionValue), (6 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetDualModeConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_AutoInjectedConvCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_AutoInjectedConvCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_InjectedDiscModeCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedDiscModeCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ExternalTrigInjectedConvConfig), (12 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigInjectedConvConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ExternalTrigInjectedConvCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigInjectedConvCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_SoftwareStartInjectedConvCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_SoftwareStartInjectedConvCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetSoftwareStartInjectedConvCmdStatus), (8 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetSoftwareStartInjectedConvCmdStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_InjectedChannelConfig), (78 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedChannelConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_InjectedSequencerLengthConfig), (22 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedSequencerLengthConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_SetInjectedOffset), (22 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_SetInjectedOffset), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetInjectedConversionValue), (26 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetInjectedConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_AnalogWatchdogCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogThresholdsConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_AnalogWatchdogSingleChannelConfig), (12 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogSingleChannelConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_TempSensorVrefintCmd), (26 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_TempSensorVrefintCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetFlagStatus), (10 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetFlagStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ClearFlag), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetITStatus), (26 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetITStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ClearITPendingBit), (8 bytes).
    Removing misc.o(.text), (0 bytes).
    Removing misc.o(.text.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(.ARM.exidx.text.NVIC_PriorityGroupConfig), (8 bytes).
    Removing misc.o(.ARM.exidx.text.NVIC_Init), (8 bytes).
    Removing misc.o(.text.NVIC_SetVectorTable), (24 bytes).
    Removing misc.o(.ARM.exidx.text.NVIC_SetVectorTable), (8 bytes).
    Removing misc.o(.text.NVIC_SystemLPConfig), (24 bytes).
    Removing misc.o(.ARM.exidx.text.NVIC_SystemLPConfig), (8 bytes).
    Removing misc.o(.text.SysTick_CLKSourceConfig), (24 bytes).
    Removing misc.o(.ARM.exidx.text.SysTick_CLKSourceConfig), (8 bytes).
    Removing stm32f10x_bkp.o(.text), (0 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_DeInit), (18 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_DeInit), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_TamperPinLevelConfig), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_TamperPinCmd), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_ITConfig), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_RTCOutputConfig), (20 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_RTCOutputConfig), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_SetRTCCalibrationValue), (20 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_SetRTCCalibrationValue), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_WriteBackupRegister), (30 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_WriteBackupRegister), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_ReadBackupRegister), (30 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_ReadBackupRegister), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_GetFlagStatus), (14 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_GetFlagStatus), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_ClearFlag), (18 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_ClearFlag), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_GetITStatus), (14 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_GetITStatus), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_ClearITPendingBit), (18 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_can.o(.text), (0 bytes).
    Removing stm32f10x_can.o(.text.CAN_DeInit), (42 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_DeInit), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_Init), (228 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_Init), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_FilterInit), (172 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_FilterInit), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_StructInit), (20 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_StructInit), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_SlaveStartBank), (44 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_SlaveStartBank), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_DBGFreeze), (18 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_DBGFreeze), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_TTComModeCmd), (92 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_TTComModeCmd), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_Transmit), (160 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_Transmit), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_TransmitStatus), (128 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_TransmitStatus), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_CancelTransmit), (32 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_CancelTransmit), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_Receive), (160 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_Receive), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_FIFORelease), (18 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_FIFORelease), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_MessagePending), (24 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_MessagePending), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_OperatingModeRequest), (146 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_OperatingModeRequest), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_Sleep), (26 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_Sleep), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_WakeUp), (44 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_WakeUp), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_GetLastErrorCode), (8 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_GetLastErrorCode), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_GetReceiveErrorCounter), (6 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_GetReceiveErrorCounter), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_GetLSBTransmitErrorCounter), (8 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_GetLSBTransmitErrorCounter), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_ITConfig), (16 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_ITConfig), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_GetFlagStatus), (52 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_GetFlagStatus), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_ClearFlag), (58 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_ClearFlag), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_GetITStatus), (280 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_GetITStatus), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_ClearITPendingBit), (172 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_cec.o(.text), (0 bytes).
    Removing stm32f10x_cec.o(.text.CEC_DeInit), (26 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_DeInit), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_Init), (28 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_Init), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_Cmd), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_ITConfig), (10 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_ITConfig), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_OwnAddressConfig), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_SetPrescaler), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_SendDataByte), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_ReceiveDataByte), (14 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_ReceiveDataByte), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_StartOfMessage), (14 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_StartOfMessage), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_EndOfMessageCmd), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_GetFlagStatus), (34 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_GetFlagStatus), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_ClearFlag), (34 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_ClearFlag), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_GetITStatus), (28 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_GetITStatus), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_ClearITPendingBit), (34 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_crc.o(.text), (0 bytes).
    Removing stm32f10x_crc.o(.text.CRC_ResetDR), (14 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_ResetDR), (8 bytes).
    Removing stm32f10x_crc.o(.text.CRC_CalcCRC), (14 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_CalcCRC), (8 bytes).
    Removing stm32f10x_crc.o(.text.CRC_CalcBlockCRC), (26 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_CalcBlockCRC), (8 bytes).
    Removing stm32f10x_crc.o(.text.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_GetCRC), (8 bytes).
    Removing stm32f10x_crc.o(.text.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_SetIDRegister), (8 bytes).
    Removing stm32f10x_crc.o(.text.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_GetIDRegister), (8 bytes).
    Removing stm32f10x_dac.o(.text), (0 bytes).
    Removing stm32f10x_dac.o(.text.DAC_DeInit), (26 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_DeInit), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_Init), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_StructInit), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_Cmd), (30 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_Cmd), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_DMACmd), (32 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_DMACmd), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_SoftwareTriggerCmd), (32 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_SoftwareTriggerCmd), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_DualSoftwareTriggerCmd), (24 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_DualSoftwareTriggerCmd), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_WaveGenerationCmd), (28 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_WaveGenerationCmd), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_SetChannel1Data), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_SetChannel2Data), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_SetDualChannelData), (24 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_SetDualChannelData), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_GetDataOutputValue), (8 bytes).
    Removing stm32f10x_dbgmcu.o(.text), (0 bytes).
    Removing stm32f10x_dbgmcu.o(.text.DBGMCU_GetREVID), (14 bytes).
    Removing stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_GetREVID), (8 bytes).
    Removing stm32f10x_dbgmcu.o(.text.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_GetDEVID), (8 bytes).
    Removing stm32f10x_dbgmcu.o(.text.DBGMCU_Config), (24 bytes).
    Removing stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_Config), (8 bytes).
    Removing stm32f10x_dma.o(.text), (0 bytes).
    Removing stm32f10x_dma.o(.text.DMA_DeInit), (268 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_DeInit), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_Init), (72 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_Init), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_StructInit), (10 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_StructInit), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_Cmd), (22 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_Cmd), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_ITConfig), (16 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_ITConfig), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_SetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_GetCurrDataCounter), (6 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_GetFlagStatus), (22 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_GetFlagStatus), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_ClearFlag), (16 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_ClearFlag), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_GetITStatus), (22 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_GetITStatus), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_exti.o(.text), (0 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_DeInit), (30 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_DeInit), (8 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_Init), (104 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_Init), (8 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_StructInit), (14 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_StructInit), (8 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_GenerateSWInterrupt), (8 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_GetFlagStatus), (18 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_GetFlagStatus), (8 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_ClearFlag), (8 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_GetITStatus), (8 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_flash.o(.text), (0 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_SetLatency), (20 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_SetLatency), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_HalfCycleAccessCmd), (24 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_HalfCycleAccessCmd), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_PrefetchBufferCmd), (24 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_PrefetchBufferCmd), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_Unlock), (30 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_Unlock), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_UnlockBank1), (30 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_UnlockBank1), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_Lock), (18 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_Lock), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_LockBank1), (18 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_LockBank1), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ErasePage), (254 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ErasePage), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_WaitForLastOperation), (118 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_EraseAllPages), (246 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseAllPages), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_EraseAllBank1Pages), (246 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseAllBank1Pages), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_WaitForLastBank1Operation), (118 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_WaitForLastBank1Operation), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_EraseOptionBytes), (436 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseOptionBytes), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetReadOutProtectionStatus), (16 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetReadOutProtectionStatus), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ProgramWord), (492 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramWord), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ProgramHalfWord), (278 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramHalfWord), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ProgramOptionByteData), (312 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramOptionByteData), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_EnableWriteProtection), (664 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_EnableWriteProtection), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ReadOutProtection), (440 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ReadOutProtection), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_UserOptionByteConfig), (328 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_UserOptionByteConfig), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetUserOptionByte), (14 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetUserOptionByte), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetWriteProtectionOptionByte), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetPrefetchBufferStatus), (16 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetPrefetchBufferStatus), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ITConfig), (24 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ITConfig), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetFlagStatus), (30 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetFlagStatus), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ClearFlag), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetStatus), (42 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetStatus), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetBank1Status), (42 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetBank1Status), (8 bytes).
    Removing stm32f10x_fsmc.o(.text), (0 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NORSRAMDeInit), (50 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMDeInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NANDDeInit), (56 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDDeInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_PCCARDDeInit), (26 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDDeInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NORSRAMInit), (192 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NANDInit), (122 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_PCCARDInit), (110 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NORSRAMStructInit), (80 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMStructInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NANDStructInit), (42 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDStructInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_PCCARDStructInit), (42 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDStructInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NORSRAMCmd), (36 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMCmd), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NANDCmd), (64 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDCmd), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_PCCARDCmd), (32 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDCmd), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NANDECCCmd), (64 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDECCCmd), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_GetECC), (22 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetECC), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_ITConfig), (84 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ITConfig), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_GetFlagStatus), (30 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetFlagStatus), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_ClearFlag), (28 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ClearFlag), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_GetITStatus), (44 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetITStatus), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_gpio.o(.text), (0 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_DeInit), (52 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_DeInit), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_AFIODeInit), (22 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_AFIODeInit), (8 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_Init), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_StructInit), (12 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_StructInit), (8 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadInputDataBit), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_ReadInputData), (6 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_ReadOutputDataBit), (10 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadOutputDataBit), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_SetBits), (8 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ResetBits), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_WriteBit), (12 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_WriteBit), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_Write), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_PinLockConfig), (16 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_PinLockConfig), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_EventOutputConfig), (30 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EventOutputConfig), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_EventOutputCmd), (10 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EventOutputCmd), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_PinRemapConfig), (128 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_PinRemapConfig), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_EXTILineConfig), (54 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EXTILineConfig), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_ETH_MediaInterfaceConfig), (10 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ETH_MediaInterfaceConfig), (8 bytes).
    Removing stm32f10x_gpio.o(.rodata..Lswitch.table.GPIO_DeInit.1), (28 bytes).
    Removing stm32f10x_i2c.o(.text), (0 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_DeInit), (42 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_DeInit), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_Init), (202 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_Init), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_StructInit), (24 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_StructInit), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_Cmd), (18 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_Cmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_DMACmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_DMACmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_DMALastTransferCmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_DMALastTransferCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GenerateSTART), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GenerateSTART), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GenerateSTOP), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GenerateSTOP), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_AcknowledgeConfig), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_AcknowledgeConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_OwnAddress2Config), (12 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_OwnAddress2Config), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_DualAddressCmd), (18 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_DualAddressCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GeneralCallCmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GeneralCallCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ITConfig), (16 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ITConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_SendData), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ReceiveData), (6 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_Send7bitAddress), (16 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_Send7bitAddress), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ReadRegister), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_SoftwareResetCmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_SoftwareResetCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_NACKPositionConfig), (24 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_NACKPositionConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_SMBusAlertConfig), (24 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_SMBusAlertConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_TransmitPEC), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_TransmitPEC), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_PECPositionConfig), (24 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_PECPositionConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_CalculatePEC), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_CalculatePEC), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GetPEC), (6 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ARPCmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ARPCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_StretchClockCmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_StretchClockCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_FastModeDutyCycleConfig), (24 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_FastModeDutyCycleConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_CheckEvent), (22 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_CheckEvent), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GetLastEvent), (12 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetLastEvent), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GetFlagStatus), (48 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetFlagStatus), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ClearFlag), (6 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ClearFlag), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GetITStatus), (28 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetITStatus), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_iwdg.o(.text), (0 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_WriteAccessCmd), (8 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_SetPrescaler), (8 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_SetReload), (8 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_ReloadCounter), (8 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_Enable), (8 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_GetFlagStatus), (18 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_GetFlagStatus), (8 bytes).
    Removing stm32f10x_pwr.o(.text), (0 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_DeInit), (26 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_DeInit), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_BackupAccessCmd), (10 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_BackupAccessCmd), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_PVDCmd), (10 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_PVDCmd), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_PVDLevelConfig), (20 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_PVDLevelConfig), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_WakeUpPinCmd), (10 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_WakeUpPinCmd), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_EnterSTOPMode), (58 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_EnterSTANDBYMode), (44 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_GetFlagStatus), (18 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_GetFlagStatus), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_ClearFlag), (18 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_ClearFlag), (8 bytes).
    Removing stm32f10x_rcc.o(.text), (0 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_DeInit), (66 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_DeInit), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_HSEConfig), (50 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_HSEConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_WaitForHSEStartUp), (48 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_WaitForHSEStartUp), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_GetFlagStatus), (38 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetFlagStatus), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_AdjustHSICalibrationValue), (22 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_AdjustHSICalibrationValue), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_HSICmd), (10 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_HSICmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_PLLConfig), (22 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_PLLConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_PLLCmd), (10 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_PLLCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_SYSCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_SYSCLKConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetSYSCLKSource), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_HCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_HCLKConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_PCLK1Config), (20 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_PCLK1Config), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_PCLK2Config), (22 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_PCLK2Config), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_ITConfig), (24 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_ITConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_USBCLKConfig), (10 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_USBCLKConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_ADCCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_ADCCLKConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_LSEConfig), (30 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_LSEConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_LSICmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_RTCCLKConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_RTCCLKCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetClocksFreq), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_AHBPeriphClockCmd), (24 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_AHBPeriphClockCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB2PeriphClockCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB1PeriphClockCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd), (24 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB2PeriphResetCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd), (24 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB1PeriphResetCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_BackupResetCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_ClockSecuritySystemCmd), (10 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClockSecuritySystemCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_MCOConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_ClearFlag), (18 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClearFlag), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_GetITStatus), (18 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetITStatus), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_rtc.o(.text), (0 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_ITConfig), (24 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_ITConfig), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_EnterConfigMode), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_EnterConfigMode), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_ExitConfigMode), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_ExitConfigMode), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_GetCounter), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetCounter), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_SetCounter), (32 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetCounter), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_SetPrescaler), (34 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetPrescaler), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_SetAlarm), (32 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetAlarm), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_GetDivider), (22 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetDivider), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_WaitForLastTask), (16 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_WaitForLastTask), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_WaitForSynchro), (24 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_WaitForSynchro), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetFlagStatus), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_ClearFlag), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_ClearFlag), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_GetITStatus), (34 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetITStatus), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_ClearITPendingBit), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_sdio.o(.text), (0 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_DeInit), (38 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DeInit), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_Init), (52 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_Init), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StructInit), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_ClockCmd), (10 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClockCmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SetPowerState), (24 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetPowerState), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetPowerState), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_ITConfig), (24 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ITConfig), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DMACmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SendCommand), (46 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendCommand), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CmdStructInit), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetCommandResponse), (14 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetCommandResponse), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetResponse), (26 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetResponse), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_DataConfig), (52 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DataConfig), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DataStructInit), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetDataCounter), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ReadData), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_WriteData), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetFIFOCount), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StartSDIOReadWait), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StopSDIOReadWait), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetSDIOReadWaitMode), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetSDIOOperation), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendSDIOSuspendCmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CommandCompletionCmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_CEATAITCmd), (18 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CEATAITCmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendCEATACmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetFlagStatus), (18 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetFlagStatus), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClearFlag), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetITStatus), (18 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetITStatus), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_spi.o(.text), (0 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_DeInit), (104 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_DeInit), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_Init), (70 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_Init), (8 bytes).
    Removing stm32f10x_spi.o(.text.I2S_Init), (162 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.I2S_Init), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_StructInit), (16 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_StructInit), (8 bytes).
    Removing stm32f10x_spi.o(.text.I2S_StructInit), (14 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.I2S_StructInit), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_Cmd), (20 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_Cmd), (8 bytes).
    Removing stm32f10x_spi.o(.text.I2S_Cmd), (20 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.I2S_Cmd), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_ITConfig), (24 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ITConfig), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_DMACmd), (16 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_DMACmd), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_SendData), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_ReceiveData), (4 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ReceiveData), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_NSSInternalSoftwareConfig), (24 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_NSSInternalSoftwareConfig), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_SSOutputCmd), (20 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_SSOutputCmd), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_DataSizeConfig), (16 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_DataSizeConfig), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_TransmitCRC), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_CalculateCRC), (20 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_CalculateCRC), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_GetCRC), (12 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_GetCRC), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_GetCRCPolynomial), (4 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_GetCRCPolynomial), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_BiDirectionalLineConfig), (24 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_BiDirectionalLineConfig), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_GetFlagStatus), (10 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_GetFlagStatus), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ClearFlag), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_GetITStatus), (24 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_GetITStatus), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_tim.o(.text), (0 bytes).
    Removing stm32f10x_tim.o(.text.TIM_DeInit), (560 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_DeInit), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_TimeBaseInit), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1Init), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2Init), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3Init), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4Init), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ICInit), (608 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ICInit), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetIC1Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC1Prescaler), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetIC2Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC2Prescaler), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetIC3Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC3Prescaler), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetIC4Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC4Prescaler), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_PWMIConfig), (596 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_PWMIConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_BDTRConfig), (40 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_BDTRConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_TimeBaseStructInit), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_TimeBaseStructInit), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OCStructInit), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OCStructInit), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ICStructInit), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ICStructInit), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_BDTRStructInit), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_BDTRStructInit), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_Cmd), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs), (24 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_CtrlPWMOutputs), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ITConfig), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ITConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GenerateEvent), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_DMAConfig), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_DMAConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_DMACmd), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_DMACmd), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_InternalClockConfig), (10 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_InternalClockConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ITRxExternalClockConfig), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ITRxExternalClockConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectInputTrigger), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectInputTrigger), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_TIxExternalClockConfig), (280 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_TIxExternalClockConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ETRClockMode1Config), (30 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRClockMode1Config), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ETRConfig), (22 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ETRClockMode2Config), (30 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRClockMode2Config), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_PrescalerConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_CounterModeConfig), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_CounterModeConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_EncoderInterfaceConfig), (48 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_EncoderInterfaceConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ForcedOC1Config), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC1Config), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ForcedOC2Config), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC2Config), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ForcedOC3Config), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC3Config), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ForcedOC4Config), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC4Config), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ARRPreloadConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectCOM), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectCOM), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectCCDMA), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectCCDMA), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_CCPreloadControl), (18 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_CCPreloadControl), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1PreloadConfig), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2PreloadConfig), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3PreloadConfig), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4PreloadConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC1FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1FastConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC2FastConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2FastConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC3FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3FastConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC4FastConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4FastConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ClearOC1Ref), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC1Ref), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ClearOC2Ref), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC2Ref), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ClearOC3Ref), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC3Ref), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ClearOC4Ref), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC4Ref), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC1PolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1PolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC1NPolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1NPolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC2PolarityConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2PolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC2NPolarityConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2NPolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC3PolarityConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3PolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC3NPolarityConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3NPolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC4PolarityConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4PolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_CCxCmd), (32 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_CCxCmd), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_CCxNCmd), (32 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_CCxNCmd), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectOCxM), (84 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOCxM), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_UpdateDisableConfig), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_UpdateDisableConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_UpdateRequestConfig), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_UpdateRequestConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectHallSensor), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectHallSensor), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectOnePulseMode), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOnePulseMode), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectOutputTrigger), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOutputTrigger), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectSlaveMode), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectMasterSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectMasterSlaveMode), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCounter), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetAutoreload), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare1), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare2), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare3), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare4), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetClockDivision), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetClockDivision), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetCapture1), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture1), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetCapture2), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture2), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetCapture3), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture3), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetCapture4), (6 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCounter), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetPrescaler), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetPrescaler), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetFlagStatus), (10 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetFlagStatus), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearFlag), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetITStatus), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_usart.o(.text), (0 bytes).
    Removing stm32f10x_usart.o(.text.USART_DeInit), (174 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_DeInit), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_Init), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_StructInit), (18 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_StructInit), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ClockInit), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_ClockStructInit), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ClockStructInit), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_Cmd), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ITConfig), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_DMACmd), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_DMACmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SetAddress), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SetAddress), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_WakeUpConfig), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_WakeUpConfig), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_ReceiverWakeUpCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ReceiverWakeUpCmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_LINBreakDetectLengthConfig), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_LINCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_LINCmd), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ReceiveData), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SendBreak), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SetGuardTime), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SetPrescaler), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SmartCardCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SmartCardCmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SmartCardNACKCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SmartCardNACKCmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_HalfDuplexCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_HalfDuplexCmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_OverSampling8Cmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_OverSampling8Cmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_OneBitMethodCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_OneBitMethodCmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_IrDAConfig), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_IrDAConfig), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_IrDACmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_IrDACmd), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_GetFlagStatus), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_ClearFlag), (6 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ClearFlag), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_GetITStatus), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_wwdg.o(.text), (0 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_DeInit), (26 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_DeInit), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_SetPrescaler), (20 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetPrescaler), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_SetWindowValue), (40 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetWindowValue), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_EnableIT), (14 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_EnableIT), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetCounter), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_Enable), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_GetFlagStatus), (14 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_GetFlagStatus), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_ClearFlag), (14 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_ClearFlag), (8 bytes).
    Removing system_stm32f10x.o(.text), (0 bytes).
    Removing system_stm32f10x.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f10x.o(.text.SystemCoreClockUpdate), (94 bytes).
    Removing system_stm32f10x.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing system_stm32f10x.o(.rodata.AHBPrescTable), (16 bytes).
    Removing core_cm3.o(.text), (0 bytes).
    Removing core_cm3.o(.text.__get_PSP), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_PSP), (8 bytes).
    Removing core_cm3.o(.text.__set_PSP), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_PSP), (8 bytes).
    Removing core_cm3.o(.text.__get_MSP), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_MSP), (8 bytes).
    Removing core_cm3.o(.text.__set_MSP), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_MSP), (8 bytes).
    Removing core_cm3.o(.text.__get_BASEPRI), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_BASEPRI), (8 bytes).
    Removing core_cm3.o(.text.__set_BASEPRI), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_BASEPRI), (8 bytes).
    Removing core_cm3.o(.text.__get_PRIMASK), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_PRIMASK), (8 bytes).
    Removing core_cm3.o(.text.__set_PRIMASK), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_PRIMASK), (8 bytes).
    Removing core_cm3.o(.text.__get_FAULTMASK), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_FAULTMASK), (8 bytes).
    Removing core_cm3.o(.text.__set_FAULTMASK), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_FAULTMASK), (8 bytes).
    Removing core_cm3.o(.text.__get_CONTROL), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_CONTROL), (8 bytes).
    Removing core_cm3.o(.text.__set_CONTROL), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_CONTROL), (8 bytes).
    Removing core_cm3.o(.text.__REV), (4 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__REV), (8 bytes).
    Removing core_cm3.o(.text.__REV16), (4 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__REV16), (8 bytes).
    Removing core_cm3.o(.text.__REVSH), (4 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__REVSH), (8 bytes).
    Removing core_cm3.o(.text.__RBIT), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__RBIT), (8 bytes).
    Removing core_cm3.o(.text.__LDREXB), (8 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__LDREXB), (8 bytes).
    Removing core_cm3.o(.text.__LDREXH), (8 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__LDREXH), (8 bytes).
    Removing core_cm3.o(.text.__LDREXW), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__LDREXW), (8 bytes).
    Removing core_cm3.o(.text.__STREXB), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__STREXB), (8 bytes).
    Removing core_cm3.o(.text.__STREXH), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__STREXH), (8 bytes).
    Removing core_cm3.o(.text.__STREXW), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__STREXW), (8 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.text.Delay_ms), (2 bytes).
    Removing main.o(.ARM.exidx.text.Delay_ms), (8 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing stm32f10x_it.o(.text), (0 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing pwm1.o(.text), (0 bytes).
    Removing pwm1.o(.text.PWM_Init), (362 bytes).
    Removing pwm1.o(.ARM.exidx.text.PWM_Init), (8 bytes).
    Removing pwm1.o(.ARM.exidx.text.PWM_SetDutyCycle), (8 bytes).
    Removing pwm1.o(.text.PWM_Start), (30 bytes).
    Removing pwm1.o(.ARM.exidx.text.PWM_Start), (8 bytes).
    Removing pwm1.o(.text.PWM_Stop), (30 bytes).
    Removing pwm1.o(.ARM.exidx.text.PWM_Stop), (8 bytes).
    Removing pwm1.o(.text.PWM_SetDeadTime), (48 bytes).
    Removing pwm1.o(.ARM.exidx.text.PWM_SetDeadTime), (8 bytes).
    Removing pwm1.o(.text.PwmConfig), (116 bytes).
    Removing pwm1.o(.ARM.exidx.text.PwmConfig), (8 bytes).
    Removing pwm2.o(.text), (0 bytes).
    Removing pwm2.o(.ARM.exidx.text.PWM2_Init), (8 bytes).
    Removing pwm2.o(.ARM.exidx.text.PWM2_SetDutyCycle), (8 bytes).
    Removing pwm2.o(.text.PWM2_Start), (10 bytes).
    Removing pwm2.o(.ARM.exidx.text.PWM2_Start), (8 bytes).
    Removing pwm2.o(.text.PWM2_Stop), (10 bytes).
    Removing pwm2.o(.ARM.exidx.text.PWM2_Stop), (8 bytes).
    Removing pwm2.o(.text.Pwm2Config_Channel4), (98 bytes).
    Removing pwm2.o(.ARM.exidx.text.Pwm2Config_Channel4), (8 bytes).
    Removing usart1.o(.text), (0 bytes).
    Removing usart1.o(.ARM.exidx.text.parse_received_data), (8 bytes).
    Removing usart1.o(.text.USART1_SendString), (50 bytes).
    Removing usart1.o(.ARM.exidx.text.USART1_SendString), (8 bytes).
    Removing usart1.o(.ARM.exidx.text.USART1_Init), (8 bytes).
    Removing usart1.o(.text.USART1_SendChar), (36 bytes).
    Removing usart1.o(.ARM.exidx.text.USART1_SendChar), (8 bytes).
    Removing usart1.o(.ARM.exidx.text.USART1_ProcessReceivedData), (8 bytes).
    Removing uart_vision.o(.text), (0 bytes).
    Removing uart_vision.o(.text.UART_Vision_Init), (62 bytes).
    Removing uart_vision.o(.ARM.exidx.text.UART_Vision_Init), (8 bytes).
    Removing uart_vision.o(.text.UART_Vision_ProcessData), (90 bytes).
    Removing uart_vision.o(.ARM.exidx.text.UART_Vision_ProcessData), (8 bytes).
    Removing uart_vision.o(.bss.vision_display_buffer), (32 bytes).
    Removing uart_vision.o(.bss.rx_buffer), (32 bytes).
    Removing uart_vision.o(.bss.rx_index), (1 bytes).
    Removing timer7.o(.text), (0 bytes).
    Removing timer7.o(.text.TIM7_Config), (116 bytes).
    Removing timer7.o(.ARM.exidx.text.TIM7_Config), (8 bytes).
    Removing timer7.o(.ARM.exidx.text.TIM7_IRQHandler), (8 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.text.OLED_W_SCL), (18 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_W_SCL), (8 bytes).
    Removing oled.o(.text.OLED_W_SDA), (18 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_W_SDA), (8 bytes).
    Removing oled.o(.text.OLED_GPIO_Init), (84 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_GPIO_Init), (8 bytes).
    Removing oled.o(.text.OLED_I2C_Start), (62 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_I2C_Start), (8 bytes).
    Removing oled.o(.text.OLED_I2C_Stop), (50 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_I2C_Stop), (8 bytes).
    Removing oled.o(.text.OLED_I2C_SendByte), (92 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_I2C_SendByte), (8 bytes).
    Removing oled.o(.text.OLED_WriteCommand), (178 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_WriteCommand), (8 bytes).
    Removing oled.o(.text.OLED_WriteData), (132 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_WriteData), (8 bytes).
    Removing oled.o(.text.OLED_Init), (284 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing oled.o(.text.OLED_Clear), (20 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Clear), (8 bytes).
    Removing oled.o(.text.OLED_Update), (54 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Update), (8 bytes).
    Removing oled.o(.text.OLED_SetCursor), (32 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_SetCursor), (8 bytes).
    Removing oled.o(.text.OLED_Pow), (22 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Pow), (8 bytes).
    Removing oled.o(.text.OLED_pnpoly), (128 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_pnpoly), (8 bytes).
    Removing oled.o(.text.OLED_IsInAngle), (124 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_IsInAngle), (8 bytes).
    Removing oled.o(.text.OLED_UpdateArea), (132 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_UpdateArea), (8 bytes).
    Removing oled.o(.text.OLED_ClearArea), (110 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ClearArea), (8 bytes).
    Removing oled.o(.text.OLED_Reverse), (48 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Reverse), (8 bytes).
    Removing oled.o(.text.OLED_ReverseArea), (106 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ReverseArea), (8 bytes).
    Removing oled.o(.text.OLED_ShowChar), (70 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowChar), (8 bytes).
    Removing oled.o(.text.OLED_ShowImage), (298 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowImage), (8 bytes).
    Removing oled.o(.text.OLED_ShowString), (320 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowString), (8 bytes).
    Removing oled.o(.text.OLED_ShowNum), (188 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowNum), (8 bytes).
    Removing oled.o(.text.OLED_ShowSignedNum), (312 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowSignedNum), (8 bytes).
    Removing oled.o(.text.OLED_ShowHexNum), (222 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowHexNum), (8 bytes).
    Removing oled.o(.text.OLED_ShowBinNum), (160 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowBinNum), (8 bytes).
    Removing oled.o(.text.OLED_ShowFloatNum), (676 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowFloatNum), (8 bytes).
    Removing oled.o(.text.OLED_Printf), (40 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Printf), (8 bytes).
    Removing oled.o(.text.OLED_DrawPoint), (48 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawPoint), (8 bytes).
    Removing oled.o(.text.OLED_GetPoint), (46 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_GetPoint), (8 bytes).
    Removing oled.o(.text.OLED_DrawLine), (708 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawLine), (8 bytes).
    Removing oled.o(.text.OLED_DrawRectangle), (336 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawRectangle), (8 bytes).
    Removing oled.o(.text.OLED_DrawTriangle), (360 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawTriangle), (8 bytes).
    Removing oled.o(.text.OLED_DrawCircle), (1192 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawCircle), (8 bytes).
    Removing oled.o(.text.OLED_DrawEllipse), (1670 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawEllipse), (8 bytes).
    Removing oled.o(.text.OLED_DrawArc), (2532 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawArc), (8 bytes).
    Removing oled.o(.bss.OLED_DisplayBuf), (1024 bytes).
    Removing oled_data.o(.text), (0 bytes).
    Removing oled_data.o(.rodata.OLED_F8x16), (1520 bytes).
    Removing oled_data.o(.rodata.OLED_F6x8), (570 bytes).
    Removing oled_data.o(.rodata.OLED_CF16x16), (245 bytes).
    Removing oled_data.o(.rodata.Diode), (32 bytes).
    Removing encoder.o(.text), (0 bytes).
    Removing encoder.o(.ARM.exidx.text.EXTI0_IRQHandler), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.EXTI2_IRQHandler), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.TIM6_IRQHandler), (8 bytes).
    Removing encoder.o(.text.Encoder_Init), (228 bytes).
    Removing encoder.o(.ARM.exidx.text.Encoder_Init), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.Encoder_GetData), (8 bytes).
    Removing encoder.o(.text.Show_Encoder), (86 bytes).
    Removing encoder.o(.ARM.exidx.text.Show_Encoder), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.EXTI1_IRQHandler), (8 bytes).
    Removing encoder.o(.text.SetMotorWithRotation), (12 bytes).
    Removing encoder.o(.ARM.exidx.text.SetMotorWithRotation), (8 bytes).
    Removing pid.o(.text), (0 bytes).
    Removing pid.o(.text.PID_Init), (22 bytes).
    Removing pid.o(.ARM.exidx.text.PID_Init), (8 bytes).
    Removing pid.o(.ARM.exidx.text.PID_SetTarget), (8 bytes).
    Removing pid.o(.ARM.exidx.text.PID_Update), (8 bytes).
    Removing servo.o(.text), (0 bytes).
    Removing servo.o(.ARM.exidx.text.Servo_Init), (8 bytes).
    Removing servo.o(.ARM.exidx.text.Servo_SetAngle), (8 bytes).
    Removing servo.o(.ARM.exidx.text.Servo2_Init), (8 bytes).
    Removing servo.o(.ARM.exidx.text.Servo2_SetAngle), (8 bytes).
    Removing servo.o(.ARM.exidx.text.Servo3_Init), (8 bytes).
    Removing servo.o(.ARM.exidx.text.Servo3_SetAngle), (8 bytes).
    Removing servo.o(.ARM.exidx.text.Servo4_Init), (8 bytes).
    Removing servo.o(.ARM.exidx.text.Servo4_SetAngle), (8 bytes).
    Removing servo.o(.ARM.exidx.text.Left), (8 bytes).
    Removing servo.o(.ARM.exidx.text.Right), (8 bytes).
    Removing servo.o(.ARM.exidx.text.Straight), (8 bytes).
    Removing servo.o(.text.servo_update), (72 bytes).
    Removing servo.o(.ARM.exidx.text.servo_update), (8 bytes).
    Removing motor_control.o(.text), (0 bytes).
    Removing motor_control.o(.text.Motor_Config), (136 bytes).
    Removing motor_control.o(.ARM.exidx.text.Motor_Config), (8 bytes).
    Removing motor_control.o(.ARM.exidx.text.MotorControl_SetSpeed), (8 bytes).
    Removing motor_control.o(.ARM.exidx.text.MotorControl_Update), (8 bytes).
    Removing gimbal.o(.text), (0 bytes).
    Removing gimbal.o(.ARM.exidx.text.Gimbal_Init), (8 bytes).
    Removing gimbal.o(.text.Gimbal_SetTargetPosition), (50 bytes).
    Removing gimbal.o(.ARM.exidx.text.Gimbal_SetTargetPosition), (8 bytes).
    Removing gimbal.o(.ARM.exidx.text.Gimbal_UpdatePosition), (8 bytes).
    Removing gimbal.o(.text.Gimbal_GetCurrentPosition), (12 bytes).
    Removing gimbal.o(.ARM.exidx.text.Gimbal_GetCurrentPosition), (8 bytes).
    Removing gimbal.o(.text.Gimbal_GetTargetPosition), (12 bytes).
    Removing gimbal.o(.ARM.exidx.text.Gimbal_GetTargetPosition), (8 bytes).
    Removing gimbal.o(.text.Gimbal_MoveXPlus), (68 bytes).
    Removing gimbal.o(.ARM.exidx.text.Gimbal_MoveXPlus), (8 bytes).
    Removing gimbal.o(.text.Gimbal_MoveXMinus), (68 bytes).
    Removing gimbal.o(.ARM.exidx.text.Gimbal_MoveXMinus), (8 bytes).
    Removing gimbal.o(.text.Gimbal_MoveYPlus), (70 bytes).
    Removing gimbal.o(.ARM.exidx.text.Gimbal_MoveYPlus), (8 bytes).
    Removing gimbal.o(.text.Gimbal_MoveYMinus), (72 bytes).
    Removing gimbal.o(.ARM.exidx.text.Gimbal_MoveYMinus), (8 bytes).
    Removing gimbal.o(.text.Gimbal_AdjustToRedTarget), (182 bytes).
    Removing gimbal.o(.ARM.exidx.text.Gimbal_AdjustToRedTarget), (8 bytes).
    Removing gimbal.o(.ARM.exidx.text.Gimbal_AdjustToCenterTarget), (8 bytes).

1164 unused section(s) (total 38996 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate_common.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _c16rtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec_accurate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmp.s                          0x00000000   Number         0  dcmp.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dgeqf.s                         0x00000000   Number         0  dgeqf.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/drnd.s                          0x00000000   Number         0  drnd.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcmp.s                          0x00000000   Number         0  fcmp.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/feqf.s                          0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fgeqf.s                         0x00000000   Number         0  fgeqf.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fpinit_empty.s                  0x00000000   Number         0  fpinit_empty.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    OLED.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    OLED_Data.c                              0x00000000   Number         0  oled_data.o ABSOLUTE
    Start/startup_stm32f10x_hd.s             0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    core_cm3.c                               0x00000000   Number         0  core_cm3.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    encoder.c                                0x00000000   Number         0  encoder.o ABSOLUTE
    gimbal.c                                 0x00000000   Number         0  gimbal.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    misc.c                                   0x00000000   Number         0  misc.o ABSOLUTE
    motor_control.c                          0x00000000   Number         0  motor_control.o ABSOLUTE
    pid.c                                    0x00000000   Number         0  pid.o ABSOLUTE
    pwm1.c                                   0x00000000   Number         0  pwm1.o ABSOLUTE
    pwm2.c                                   0x00000000   Number         0  pwm2.o ABSOLUTE
    servo.c                                  0x00000000   Number         0  servo.o ABSOLUTE
    stm32f10x_adc.c                          0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    stm32f10x_bkp.c                          0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    stm32f10x_can.c                          0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    stm32f10x_cec.c                          0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    stm32f10x_crc.c                          0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    stm32f10x_dac.c                          0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    stm32f10x_dbgmcu.c                       0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    stm32f10x_dma.c                          0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    stm32f10x_exti.c                         0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    stm32f10x_flash.c                        0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    stm32f10x_fsmc.c                         0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    stm32f10x_gpio.c                         0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    stm32f10x_i2c.c                          0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    stm32f10x_iwdg.c                         0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    stm32f10x_pwr.c                          0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    stm32f10x_rcc.c                          0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    stm32f10x_rtc.c                          0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    stm32f10x_sdio.c                         0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    stm32f10x_spi.c                          0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    stm32f10x_tim.c                          0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    stm32f10x_usart.c                        0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    stm32f10x_wwdg.c                         0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    timer7.c                                 0x00000000   Number         0  timer7.o ABSOLUTE
    uart_vision.c                            0x00000000   Number         0  uart_vision.o ABSOLUTE
    usart1.c                                 0x00000000   Number         0  usart1.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       92  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000194   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_null                           0x080001b0   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x080001b4   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001d0   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x080001d0   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000017  0x080001d6   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080001da   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001dc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001dc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x080001dc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x080001dc   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001dc   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x080001dc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000011          0x080001dc   Section        6  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001e2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000014          0x080001e2   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000014)
    .ARM.Collect$$libinit$$00000015          0x080001ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x080001ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x080001ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x080001ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x080001ee   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x080001f0   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x080001f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x080001f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x080001f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x080001f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x080001f2   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x080001f4   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001f4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001f4   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001fa   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001fa   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001fe   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001fe   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000206   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000208   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000208   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800020c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000214   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000254   Section        0  __2sprintf.o(.text)
    .text                                    0x08000280   Section        0  _printf_dec.o(.text)
    .text                                    0x080002f8   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000480   Section        0  __0sscanf.o(.text)
    .text                                    0x080004bc   Section        0  _scanf_int.o(.text)
    .text                                    0x08000608   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000688   Section        0  heapauxi.o(.text)
    .text                                    0x0800068e   Section        0  _printf_intcommon.o(.text)
    _printf_input_char                       0x08000741   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000740   Section        0  _printf_char_common.o(.text)
    .text                                    0x08000770   Section        0  _sputc.o(.text)
    .text                                    0x0800077a   Section        0  _chval.o(.text)
    _scanf_char_input                        0x08000799   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x08000798   Section        0  scanf_char.o(.text)
    .text                                    0x080007c4   Section        0  _sgetc.o(.text)
    .text                                    0x08000804   Section        0  isspace.o(.text)
    .text                                    0x08000818   Section        0  _scanf.o(.text)
    .text                                    0x08000b8c   Section        8  libspace.o(.text)
    .text                                    0x08000b94   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000be0   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08000bf0   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000bf8   Section        0  exit.o(.text)
    .text                                    0x08000c0c   Section        0  sys_exit.o(.text)
    .text                                    0x08000c18   Section        2  use_no_semi.o(.text)
    .text                                    0x08000c1a   Section        0  indicate_semi.o(.text)
    [Anonymous Symbol]                       0x08000c1c   Section        0  stm32f10x_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x08000c20   Section        0  stm32f10x_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x08000c24   Section        0  encoder.o(.text.EXTI0_IRQHandler)
    [Anonymous Symbol]                       0x08000d0c   Section        0  encoder.o(.text.EXTI1_IRQHandler)
    [Anonymous Symbol]                       0x08000d14   Section        0  encoder.o(.text.EXTI2_IRQHandler)
    [Anonymous Symbol]                       0x08000d80   Section        0  stm32f10x_exti.o(.text.EXTI_ClearITPendingBit)
    [Anonymous Symbol]                       0x08000d8c   Section        0  stm32f10x_exti.o(.text.EXTI_GetITStatus)
    [Anonymous Symbol]                       0x08000dac   Section        0  encoder.o(.text.Encoder_GetData)
    [Anonymous Symbol]                       0x08000dd0   Section        0  stm32f10x_gpio.o(.text.GPIO_Init)
    [Anonymous Symbol]                       0x08000e8c   Section        0  stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit)
    [Anonymous Symbol]                       0x08000e98   Section        0  stm32f10x_gpio.o(.text.GPIO_ResetBits)
    [Anonymous Symbol]                       0x08000e9c   Section        0  stm32f10x_gpio.o(.text.GPIO_SetBits)
    [Anonymous Symbol]                       0x08000ea0   Section        0  gimbal.o(.text.Gimbal_AdjustToCenterTarget)
    [Anonymous Symbol]                       0x08000f40   Section        0  gimbal.o(.text.Gimbal_Init)
    [Anonymous Symbol]                       0x08000f84   Section        0  gimbal.o(.text.Gimbal_UpdatePosition)
    [Anonymous Symbol]                       0x08001028   Section        0  stm32f10x_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x0800102c   Section        0  servo.o(.text.Left)
    [Anonymous Symbol]                       0x0800103c   Section        0  stm32f10x_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x08001040   Section        0  motor_control.o(.text.MotorControl_SetSpeed)
    [Anonymous Symbol]                       0x08001054   Section        0  motor_control.o(.text.MotorControl_Update)
    [Anonymous Symbol]                       0x08001084   Section        0  stm32f10x_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x08001088   Section        0  misc.o(.text.NVIC_Init)
    [Anonymous Symbol]                       0x080010e8   Section        0  pid.o(.text.PID_SetTarget)
    [Anonymous Symbol]                       0x080010f4   Section        0  pid.o(.text.PID_Update)
    [Anonymous Symbol]                       0x08001170   Section        0  pwm2.o(.text.PWM2_Init)
    [Anonymous Symbol]                       0x080012d4   Section        0  pwm2.o(.text.PWM2_SetDutyCycle)
    [Anonymous Symbol]                       0x08001364   Section        0  pwm1.o(.text.PWM_SetDutyCycle)
    [Anonymous Symbol]                       0x08001404   Section        0  stm32f10x_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x08001408   Section        0  stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd)
    [Anonymous Symbol]                       0x08001420   Section        0  stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd)
    [Anonymous Symbol]                       0x08001438   Section        0  stm32f10x_rcc.o(.text.RCC_GetClocksFreq)
    [Anonymous Symbol]                       0x080014c4   Section        0  servo.o(.text.Right)
    [Anonymous Symbol]                       0x080014d4   Section        0  stm32f10x_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x080014d8   Section        0  servo.o(.text.Servo2_Init)
    [Anonymous Symbol]                       0x0800153c   Section        0  servo.o(.text.Servo2_SetAngle)
    [Anonymous Symbol]                       0x08001580   Section        0  servo.o(.text.Servo3_Init)
    [Anonymous Symbol]                       0x080015fc   Section        0  servo.o(.text.Servo3_SetAngle)
    [Anonymous Symbol]                       0x08001640   Section        0  servo.o(.text.Servo4_Init)
    [Anonymous Symbol]                       0x080016ac   Section        0  servo.o(.text.Servo4_SetAngle)
    [Anonymous Symbol]                       0x080016f0   Section        0  servo.o(.text.Servo_Init)
    [Anonymous Symbol]                       0x08001754   Section        0  servo.o(.text.Servo_SetAngle)
    [Anonymous Symbol]                       0x08001798   Section        0  servo.o(.text.Straight)
    [Anonymous Symbol]                       0x080017a4   Section        0  stm32f10x_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x080017a8   Section        0  system_stm32f10x.o(.text.SystemInit)
    [Anonymous Symbol]                       0x080018b8   Section        0  encoder.o(.text.TIM6_IRQHandler)
    [Anonymous Symbol]                       0x08001904   Section        0  timer7.o(.text.TIM7_IRQHandler)
    [Anonymous Symbol]                       0x0800192c   Section        0  stm32f10x_tim.o(.text.TIM_ARRPreloadConfig)
    [Anonymous Symbol]                       0x08001940   Section        0  stm32f10x_tim.o(.text.TIM_ClearITPendingBit)
    [Anonymous Symbol]                       0x08001948   Section        0  stm32f10x_tim.o(.text.TIM_Cmd)
    [Anonymous Symbol]                       0x0800195c   Section        0  stm32f10x_tim.o(.text.TIM_GetCounter)
    [Anonymous Symbol]                       0x08001960   Section        0  stm32f10x_tim.o(.text.TIM_GetITStatus)
    [Anonymous Symbol]                       0x08001978   Section        0  stm32f10x_tim.o(.text.TIM_OC1Init)
    [Anonymous Symbol]                       0x080019e8   Section        0  stm32f10x_tim.o(.text.TIM_OC1PreloadConfig)
    [Anonymous Symbol]                       0x080019f4   Section        0  stm32f10x_tim.o(.text.TIM_OC2Init)
    [Anonymous Symbol]                       0x08001a68   Section        0  stm32f10x_tim.o(.text.TIM_OC2PreloadConfig)
    [Anonymous Symbol]                       0x08001a78   Section        0  stm32f10x_tim.o(.text.TIM_OC3Init)
    [Anonymous Symbol]                       0x08001aec   Section        0  stm32f10x_tim.o(.text.TIM_OC3PreloadConfig)
    [Anonymous Symbol]                       0x08001af8   Section        0  stm32f10x_tim.o(.text.TIM_OC4Init)
    [Anonymous Symbol]                       0x08001b54   Section        0  stm32f10x_tim.o(.text.TIM_OC4PreloadConfig)
    [Anonymous Symbol]                       0x08001b64   Section        0  stm32f10x_tim.o(.text.TIM_SetCompare1)
    [Anonymous Symbol]                       0x08001b68   Section        0  stm32f10x_tim.o(.text.TIM_SetCompare2)
    [Anonymous Symbol]                       0x08001b6c   Section        0  stm32f10x_tim.o(.text.TIM_SetCompare3)
    [Anonymous Symbol]                       0x08001b70   Section        0  stm32f10x_tim.o(.text.TIM_SetCompare4)
    [Anonymous Symbol]                       0x08001b78   Section        0  stm32f10x_tim.o(.text.TIM_TimeBaseInit)
    [Anonymous Symbol]                       0x08001c24   Section        0  stm32f10x_it.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x08001c58   Section        0  usart1.o(.text.USART1_Init)
    [Anonymous Symbol]                       0x08001cdc   Section        0  usart1.o(.text.USART1_ProcessReceivedData)
    [Anonymous Symbol]                       0x08001d30   Section        0  stm32f10x_usart.o(.text.USART_ClearITPendingBit)
    [Anonymous Symbol]                       0x08001d40   Section        0  stm32f10x_usart.o(.text.USART_Cmd)
    [Anonymous Symbol]                       0x08001d54   Section        0  stm32f10x_usart.o(.text.USART_GetFlagStatus)
    [Anonymous Symbol]                       0x08001d60   Section        0  stm32f10x_usart.o(.text.USART_GetITStatus)
    [Anonymous Symbol]                       0x08001d8c   Section        0  stm32f10x_usart.o(.text.USART_ITConfig)
    [Anonymous Symbol]                       0x08001dbc   Section        0  stm32f10x_usart.o(.text.USART_Init)
    [Anonymous Symbol]                       0x08001e74   Section        0  stm32f10x_usart.o(.text.USART_ReceiveData)
    [Anonymous Symbol]                       0x08001e7c   Section        0  stm32f10x_usart.o(.text.USART_SendData)
    [Anonymous Symbol]                       0x08001e84   Section        0  stm32f10x_it.o(.text.UsageFault_Handler)
    [Anonymous Symbol]                       0x08001e88   Section        0  main.o(.text.main)
    [Anonymous Symbol]                       0x08001ea4   Section        0  usart1.o(.text.parse_received_data)
    i._is_digit                              0x08002060   Section        0  __printf_wp.o(i._is_digit)
    locale$$code                             0x08002070   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$fadd                               0x0800209c   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x080020ab   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcmp                               0x08002160   Section       84  fcmp.o(x$fpl$fcmp)
    x$fpl$fcmpinf                            0x080021b4   Section       24  fcmpi.o(x$fpl$fcmpinf)
    _fdiv1                                   0x080021cd   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$fdiv                               0x080021cc   Section      388  fdiv.o(x$fpl$fdiv)
    x$fpl$feqf                               0x08002350   Section      104  feqf.o(x$fpl$feqf)
    x$fpl$ffix                               0x080023b8   Section       54  ffix.o(x$fpl$ffix)
    x$fpl$fflt                               0x080023f0   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$ffltu                              0x08002420   Section       38  fflt_clz.o(x$fpl$ffltu)
    x$fpl$fgeqf                              0x08002448   Section      104  fgeqf.o(x$fpl$fgeqf)
    x$fpl$fleqf                              0x080024b0   Section      104  fleqf.o(x$fpl$fleqf)
    x$fpl$fmul                               0x08002518   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x0800261a   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x080026a6   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$fsub                               0x080026b0   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x080026bf   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    maptable                                 0x0800279a   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x0800279a   Section       17  __printf_flags_ss_wp.o(.constdata)
    x$fpl$usenofp                            0x0800279a   Section        0  usenofp.o(x$fpl$usenofp)
    ADCPrescTable                            0x080027ab   Data           4  stm32f10x_rcc.o(.rodata.ADCPrescTable)
    [Anonymous Symbol]                       0x080027ab   Section        0  stm32f10x_rcc.o(.rodata.ADCPrescTable)
    APBAHBPrescTable                         0x080027af   Data          16  stm32f10x_rcc.o(.rodata.APBAHBPrescTable)
    [Anonymous Symbol]                       0x080027af   Section        0  stm32f10x_rcc.o(.rodata.APBAHBPrescTable)
    .L.str.2                                 0x080027bf   Data           9  usart1.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x080027bf   Section        0  usart1.o(.rodata.str1.1)
    .L.str                                   0x080027c8   Data           8  usart1.o(.rodata.str1.1)
    locale$$data                             0x080027f0   Section      272  lc_ctype_c.o(locale$$data)
    __lcctype_c_name                         0x080027f4   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x080027fc   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x08002900   Data           0  lc_ctype_c.o(locale$$data)
    EXTI2_IRQHandler.last_tim6_count         0x20000000   Data           2  encoder.o(.data.EXTI2_IRQHandler.last_tim6_count)
    [Anonymous Symbol]                       0x20000000   Section        0  encoder.o(.data.EXTI2_IRQHandler.last_tim6_count)
    .bss                                     0x20000018   Section       96  libspace.o(.bss)
    TIM6_IRQHandler.last_absolute_count      0x20000078   Data           4  encoder.o(.bss.TIM6_IRQHandler.last_absolute_count)
    [Anonymous Symbol]                       0x20000078   Section        0  encoder.o(.bss.TIM6_IRQHandler.last_absolute_count)
    encoder                                  0x20000080   Data          16  encoder.o(.bss.encoder)
    [Anonymous Symbol]                       0x20000080   Section        0  encoder.o(.bss.encoder)
    motor                                    0x20000090   Data          40  motor_control.o(.bss.motor)
    [Anonymous Symbol]                       0x20000090   Section        0  motor_control.o(.bss.motor)
    pwmPeriod                                0x200000b8   Data           2  pwm1.o(.bss.pwmPeriod)
    [Anonymous Symbol]                       0x200000b8   Section        0  pwm1.o(.bss.pwmPeriod)
    pwmPeriod                                0x200000ba   Data           2  pwm2.o(.bss.pwmPeriod)
    [Anonymous Symbol]                       0x200000ba   Section        0  pwm2.o(.bss.pwmPeriod)
    rx_buffer                                0x200000bc   Data         256  usart1.o(.bss.rx_buffer)
    [Anonymous Symbol]                       0x200000bc   Section        0  usart1.o(.bss.rx_buffer)
    rx_index                                 0x200001bc   Data           2  usart1.o(.bss.rx_index)
    [Anonymous Symbol]                       0x200001bc   Section        0  usart1.o(.bss.rx_index)
    Heap_Mem                                 0x200001d8   Data         512  startup_stm32f10x_hd.o(HEAP)
    HEAP                                     0x200001d8   Section      512  startup_stm32f10x_hd.o(HEAP)
    Stack_Mem                                0x200003d8   Data        1024  startup_stm32f10x_hd.o(STACK)
    STACK                                    0x200003d8   Section     1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x200007d8   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __fp_init_empty                          0x00000000   Number         0  fpinit_empty.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_mbtowc                             - Undefined Weak Reference
    _scanf_real                               - Undefined Weak Reference
    _scanf_string                             - Undefined Weak Reference
    _scanf_wctomb                             - Undefined Weak Reference
    _scanf_wstring                            - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    84  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x08000143   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000195   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_null                       0x080001b1   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x080001b5   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_d                                0x080001d1   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent                          0x080001d1   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_percent_end                      0x080001d7   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080001db   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001dd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x080001dd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_common                  0x080001dd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_preinit_1                  0x080001dd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x080001dd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x080001dd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_user_alloc_1               0x080001dd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_lc_collate_1               0x080001e3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_2                 0x080001e3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000014)
    __rt_lib_init_alloca_1                   0x080001ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x080001ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x080001ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x080001ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x080001ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x080001ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_trap_1                  0x080001ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x080001ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_lc_ctype_1                 0x080001ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x080001ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_1               0x080001ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x080001ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_return                     0x080001ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x080001ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x080001ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_shutdown                        0x080001f1   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x080001f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x080001f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x080001f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x080001f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x080001f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x080001f5   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001f5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001f5   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001fb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001fb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001ff   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001ff   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000207   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000209   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000209   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800020d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000215   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x0800022f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x08000231   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __2sprintf                               0x08000255   Thumb Code    38  __2sprintf.o(.text)
    _printf_int_dec                          0x08000281   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x080002f9   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __0sscanf                                0x08000481   Thumb Code    52  __0sscanf.o(.text)
    _scanf_int                               0x080004bd   Thumb Code   332  _scanf_int.o(.text)
    strcmp                                   0x08000609   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x08000689   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800068b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800068d   Thumb Code     2  heapauxi.o(.text)
    _printf_int_common                       0x0800068f   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_char_common                      0x0800074b   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000771   Thumb Code    10  _sputc.o(.text)
    _chval                                   0x0800077b   Thumb Code    28  _chval.o(.text)
    __vfscanf_char                           0x080007a5   Thumb Code    24  scanf_char.o(.text)
    _sgetc                                   0x080007c5   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x080007e3   Thumb Code    34  _sgetc.o(.text)
    isspace                                  0x08000805   Thumb Code    18  isspace.o(.text)
    __vfscanf                                0x08000819   Thumb Code   878  _scanf.o(.text)
    __user_libspace                          0x08000b8d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000b8d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000b8d   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000b95   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08000be1   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x08000bf1   Thumb Code     8  rt_locale_intlibspace.o(.text)
    exit                                     0x08000bf9   Thumb Code    18  exit.o(.text)
    _sys_exit                                0x08000c0d   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000c19   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000c19   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08000c1b   Thumb Code     0  indicate_semi.o(.text)
    BusFault_Handler                         0x08000c1d   Thumb Code     2  stm32f10x_it.o(.text.BusFault_Handler)
    DebugMon_Handler                         0x08000c21   Thumb Code     2  stm32f10x_it.o(.text.DebugMon_Handler)
    EXTI0_IRQHandler                         0x08000c25   Thumb Code   230  encoder.o(.text.EXTI0_IRQHandler)
    EXTI1_IRQHandler                         0x08000d0d   Thumb Code     6  encoder.o(.text.EXTI1_IRQHandler)
    EXTI2_IRQHandler                         0x08000d15   Thumb Code   106  encoder.o(.text.EXTI2_IRQHandler)
    EXTI_ClearITPendingBit                   0x08000d81   Thumb Code    12  stm32f10x_exti.o(.text.EXTI_ClearITPendingBit)
    EXTI_GetITStatus                         0x08000d8d   Thumb Code    30  stm32f10x_exti.o(.text.EXTI_GetITStatus)
    Encoder_GetData                          0x08000dad   Thumb Code    34  encoder.o(.text.Encoder_GetData)
    GPIO_Init                                0x08000dd1   Thumb Code   188  stm32f10x_gpio.o(.text.GPIO_Init)
    GPIO_ReadInputDataBit                    0x08000e8d   Thumb Code    10  stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x08000e99   Thumb Code     4  stm32f10x_gpio.o(.text.GPIO_ResetBits)
    GPIO_SetBits                             0x08000e9d   Thumb Code     4  stm32f10x_gpio.o(.text.GPIO_SetBits)
    Gimbal_AdjustToCenterTarget              0x08000ea1   Thumb Code   160  gimbal.o(.text.Gimbal_AdjustToCenterTarget)
    Gimbal_Init                              0x08000f41   Thumb Code    68  gimbal.o(.text.Gimbal_Init)
    Gimbal_UpdatePosition                    0x08000f85   Thumb Code   164  gimbal.o(.text.Gimbal_UpdatePosition)
    HardFault_Handler                        0x08001029   Thumb Code     2  stm32f10x_it.o(.text.HardFault_Handler)
    Left                                     0x0800102d   Thumb Code    14  servo.o(.text.Left)
    MemManage_Handler                        0x0800103d   Thumb Code     2  stm32f10x_it.o(.text.MemManage_Handler)
    MotorControl_SetSpeed                    0x08001041   Thumb Code    18  motor_control.o(.text.MotorControl_SetSpeed)
    MotorControl_Update                      0x08001055   Thumb Code    48  motor_control.o(.text.MotorControl_Update)
    NMI_Handler                              0x08001085   Thumb Code     2  stm32f10x_it.o(.text.NMI_Handler)
    NVIC_Init                                0x08001089   Thumb Code    94  misc.o(.text.NVIC_Init)
    PID_SetTarget                            0x080010e9   Thumb Code    10  pid.o(.text.PID_SetTarget)
    PID_Update                               0x080010f5   Thumb Code   124  pid.o(.text.PID_Update)
    PWM2_Init                                0x08001171   Thumb Code   356  pwm2.o(.text.PWM2_Init)
    PWM2_SetDutyCycle                        0x080012d5   Thumb Code   144  pwm2.o(.text.PWM2_SetDutyCycle)
    PWM_SetDutyCycle                         0x08001365   Thumb Code   160  pwm1.o(.text.PWM_SetDutyCycle)
    PendSV_Handler                           0x08001405   Thumb Code     2  stm32f10x_it.o(.text.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x08001409   Thumb Code    24  stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08001421   Thumb Code    24  stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08001439   Thumb Code   140  stm32f10x_rcc.o(.text.RCC_GetClocksFreq)
    Right                                    0x080014c5   Thumb Code    14  servo.o(.text.Right)
    SVC_Handler                              0x080014d5   Thumb Code     2  stm32f10x_it.o(.text.SVC_Handler)
    Servo2_Init                              0x080014d9   Thumb Code   100  servo.o(.text.Servo2_Init)
    Servo2_SetAngle                          0x0800153d   Thumb Code    68  servo.o(.text.Servo2_SetAngle)
    Servo3_Init                              0x08001581   Thumb Code   124  servo.o(.text.Servo3_Init)
    Servo3_SetAngle                          0x080015fd   Thumb Code    68  servo.o(.text.Servo3_SetAngle)
    Servo4_Init                              0x08001641   Thumb Code   106  servo.o(.text.Servo4_Init)
    Servo4_SetAngle                          0x080016ad   Thumb Code    68  servo.o(.text.Servo4_SetAngle)
    Servo_Init                               0x080016f1   Thumb Code   100  servo.o(.text.Servo_Init)
    Servo_SetAngle                           0x08001755   Thumb Code    68  servo.o(.text.Servo_SetAngle)
    Straight                                 0x08001799   Thumb Code    12  servo.o(.text.Straight)
    SysTick_Handler                          0x080017a5   Thumb Code     2  stm32f10x_it.o(.text.SysTick_Handler)
    SystemInit                               0x080017a9   Thumb Code   272  system_stm32f10x.o(.text.SystemInit)
    TIM6_IRQHandler                          0x080018b9   Thumb Code    76  encoder.o(.text.TIM6_IRQHandler)
    TIM7_IRQHandler                          0x08001905   Thumb Code    40  timer7.o(.text.TIM7_IRQHandler)
    TIM_ARRPreloadConfig                     0x0800192d   Thumb Code    20  stm32f10x_tim.o(.text.TIM_ARRPreloadConfig)
    TIM_ClearITPendingBit                    0x08001941   Thumb Code     6  stm32f10x_tim.o(.text.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08001949   Thumb Code    18  stm32f10x_tim.o(.text.TIM_Cmd)
    TIM_GetCounter                           0x0800195d   Thumb Code     4  stm32f10x_tim.o(.text.TIM_GetCounter)
    TIM_GetITStatus                          0x08001961   Thumb Code    24  stm32f10x_tim.o(.text.TIM_GetITStatus)
    TIM_OC1Init                              0x08001979   Thumb Code   112  stm32f10x_tim.o(.text.TIM_OC1Init)
    TIM_OC1PreloadConfig                     0x080019e9   Thumb Code    12  stm32f10x_tim.o(.text.TIM_OC1PreloadConfig)
    TIM_OC2Init                              0x080019f5   Thumb Code   116  stm32f10x_tim.o(.text.TIM_OC2Init)
    TIM_OC2PreloadConfig                     0x08001a69   Thumb Code    14  stm32f10x_tim.o(.text.TIM_OC2PreloadConfig)
    TIM_OC3Init                              0x08001a79   Thumb Code   116  stm32f10x_tim.o(.text.TIM_OC3Init)
    TIM_OC3PreloadConfig                     0x08001aed   Thumb Code    12  stm32f10x_tim.o(.text.TIM_OC3PreloadConfig)
    TIM_OC4Init                              0x08001af9   Thumb Code    92  stm32f10x_tim.o(.text.TIM_OC4Init)
    TIM_OC4PreloadConfig                     0x08001b55   Thumb Code    14  stm32f10x_tim.o(.text.TIM_OC4PreloadConfig)
    TIM_SetCompare1                          0x08001b65   Thumb Code     4  stm32f10x_tim.o(.text.TIM_SetCompare1)
    TIM_SetCompare2                          0x08001b69   Thumb Code     4  stm32f10x_tim.o(.text.TIM_SetCompare2)
    TIM_SetCompare3                          0x08001b6d   Thumb Code     4  stm32f10x_tim.o(.text.TIM_SetCompare3)
    TIM_SetCompare4                          0x08001b71   Thumb Code     6  stm32f10x_tim.o(.text.TIM_SetCompare4)
    TIM_TimeBaseInit                         0x08001b79   Thumb Code   170  stm32f10x_tim.o(.text.TIM_TimeBaseInit)
    USART1_IRQHandler                        0x08001c25   Thumb Code    52  stm32f10x_it.o(.text.USART1_IRQHandler)
    USART1_Init                              0x08001c59   Thumb Code   132  usart1.o(.text.USART1_Init)
    USART1_ProcessReceivedData               0x08001cdd   Thumb Code    84  usart1.o(.text.USART1_ProcessReceivedData)
    USART_ClearITPendingBit                  0x08001d31   Thumb Code    14  stm32f10x_usart.o(.text.USART_ClearITPendingBit)
    USART_Cmd                                0x08001d41   Thumb Code    20  stm32f10x_usart.o(.text.USART_Cmd)
    USART_GetFlagStatus                      0x08001d55   Thumb Code    10  stm32f10x_usart.o(.text.USART_GetFlagStatus)
    USART_GetITStatus                        0x08001d61   Thumb Code    42  stm32f10x_usart.o(.text.USART_GetITStatus)
    USART_ITConfig                           0x08001d8d   Thumb Code    46  stm32f10x_usart.o(.text.USART_ITConfig)
    USART_Init                               0x08001dbd   Thumb Code   182  stm32f10x_usart.o(.text.USART_Init)
    USART_ReceiveData                        0x08001e75   Thumb Code     8  stm32f10x_usart.o(.text.USART_ReceiveData)
    USART_SendData                           0x08001e7d   Thumb Code     8  stm32f10x_usart.o(.text.USART_SendData)
    UsageFault_Handler                       0x08001e85   Thumb Code     2  stm32f10x_it.o(.text.UsageFault_Handler)
    main                                     0x08001e89   Thumb Code    26  main.o(.text.main)
    parse_received_data                      0x08001ea5   Thumb Code   360  usart1.o(.text.parse_received_data)
    _is_digit                                0x08002061   Thumb Code    14  __printf_wp.o(i._is_digit)
    _get_lc_ctype                            0x08002071   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_fadd                             0x0800209d   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x0800209d   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __aeabi_fcmpeq                           0x08002161   Thumb Code     0  fcmp.o(x$fpl$fcmp)
    _feq                                     0x08002161   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    _fneq                                    0x0800216f   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    __aeabi_fcmpgt                           0x0800217d   Thumb Code     0  fcmp.o(x$fpl$fcmp)
    _fgr                                     0x0800217d   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    __aeabi_fcmpge                           0x0800218b   Thumb Code     0  fcmp.o(x$fpl$fcmp)
    _fgeq                                    0x0800218b   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    __aeabi_fcmple                           0x08002199   Thumb Code     0  fcmp.o(x$fpl$fcmp)
    _fleq                                    0x08002199   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    __aeabi_fcmplt                           0x080021a7   Thumb Code     0  fcmp.o(x$fpl$fcmp)
    _fls                                     0x080021a7   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    __fpl_fcmp_Inf                           0x080021b5   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __aeabi_fdiv                             0x080021cd   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x080021cd   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_cfcmpeq                          0x08002351   Thumb Code     0  feqf.o(x$fpl$feqf)
    _fcmpeq                                  0x08002351   Thumb Code   104  feqf.o(x$fpl$feqf)
    __aeabi_f2iz                             0x080023b9   Thumb Code     0  ffix.o(x$fpl$ffix)
    _ffix                                    0x080023b9   Thumb Code    54  ffix.o(x$fpl$ffix)
    __aeabi_i2f                              0x080023f1   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x080023f1   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_ui2f                             0x08002421   Thumb Code     0  fflt_clz.o(x$fpl$ffltu)
    _ffltu                                   0x08002421   Thumb Code    38  fflt_clz.o(x$fpl$ffltu)
    _fcmpge                                  0x08002449   Thumb Code   104  fgeqf.o(x$fpl$fgeqf)
    __aeabi_cfcmple                          0x080024b1   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    _fcmple                                  0x080024b1   Thumb Code   104  fleqf.o(x$fpl$fleqf)
    __fpl_fcmple_InfNaN                      0x08002503   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    __aeabi_fmul                             0x08002519   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x08002519   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x0800261b   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x080026a7   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_fsub                             0x080026b1   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x080026b1   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    __I$use$fp                               0x0800279a   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x080027d0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080027f0   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x080027fd   Data           0  lc_ctype_c.o(locale$$data)
    SystemCoreClock                          0x20000004   Data           4  system_stm32f10x.o(.data.SystemCoreClock)
    currentPos                               0x20000008   Data           4  gimbal.o(.data.currentPos)
    path                                     0x2000000c   Data           4  encoder.o(.data.path)
    redx                                     0x20000010   Data           2  usart1.o(.data.redx)
    redy                                     0x20000012   Data           2  usart1.o(.data.redy)
    targetPos                                0x20000014   Data           4  gimbal.o(.data.targetPos)
    __libspace_start                         0x20000018   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000078   Data           0  libspace.o(.bss)
    centerx                                  0x2000007c   Data           2  usart1.o(.bss.centerx)
    centery                                  0x2000007e   Data           2  usart1.o(.bss.centery)
    target_absolute_count                    0x200001c0   Data           4  encoder.o(.bss.target_absolute_count)
    vertex_x                                 0x200001c4   Data           8  usart1.o(.bss.vertex_x)
    vertex_y                                 0x200001cc   Data           8  usart1.o(.bss.vertex_y)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002918, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00002900, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO         1190    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000008   Code   RO         1589  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x0000005c   Code   RO         2033    !!!scatter          c_w.l(__scatter.o)
    0x08000194   0x08000194   0x0000001a   Code   RO         2037    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001ae   0x080001ae   0x00000002   PAD
    0x080001b0   0x080001b0   0x00000002   Code   RO         2034    !!handler_null      c_w.l(__scatter.o)
    0x080001b2   0x080001b2   0x00000002   PAD
    0x080001b4   0x080001b4   0x0000001c   Code   RO         2039    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001d0   0x080001d0   0x00000000   Code   RO         1576    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001d0   0x080001d0   0x00000006   Code   RO         1575    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x080001d6   0x080001d6   0x00000004   Code   RO         1712    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080001da   0x080001da   0x00000002   Code   RO         1883    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001dc   0x080001dc   0x00000000   Code   RO         1896    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001dc   0x080001dc   0x00000000   Code   RO         1898    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001dc   0x080001dc   0x00000000   Code   RO         1900    .ARM.Collect$$libinit$$00000006  c_w.l(libinit2.o)
    0x080001dc   0x080001dc   0x00000000   Code   RO         1903    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001dc   0x080001dc   0x00000000   Code   RO         1905    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001dc   0x080001dc   0x00000000   Code   RO         1907    .ARM.Collect$$libinit$$00000010  c_w.l(libinit2.o)
    0x080001dc   0x080001dc   0x00000006   Code   RO         1908    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001e2   0x080001e2   0x00000000   Code   RO         1910    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001e2   0x080001e2   0x0000000c   Code   RO         1911    .ARM.Collect$$libinit$$00000014  c_w.l(libinit2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         1912    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         1914    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         1916    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         1918    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         1920    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         1922    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         1924    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         1926    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         1928    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         1930    .ARM.Collect$$libinit$$00000027  c_w.l(libinit2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         1934    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         1936    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         1938    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001ee   0x080001ee   0x00000000   Code   RO         1940    .ARM.Collect$$libinit$$00000034  c_w.l(libinit2.o)
    0x080001ee   0x080001ee   0x00000002   Code   RO         1941    .ARM.Collect$$libinit$$00000035  c_w.l(libinit2.o)
    0x080001f0   0x080001f0   0x00000002   Code   RO         1969    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001f2   0x080001f2   0x00000000   Code   RO         1984    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001f2   0x080001f2   0x00000000   Code   RO         1986    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001f2   0x080001f2   0x00000000   Code   RO         1989    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x080001f2   0x080001f2   0x00000000   Code   RO         1992    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x080001f2   0x080001f2   0x00000000   Code   RO         1994    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001f2   0x080001f2   0x00000000   Code   RO         1997    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080001f2   0x080001f2   0x00000002   Code   RO         1998    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x080001f4   0x080001f4   0x00000000   Code   RO         1651    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001f4   0x080001f4   0x00000000   Code   RO         1782    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001f4   0x080001f4   0x00000006   Code   RO         1794    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001fa   0x080001fa   0x00000000   Code   RO         1784    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001fa   0x080001fa   0x00000004   Code   RO         1785    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001fe   0x080001fe   0x00000000   Code   RO         1787    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001fe   0x080001fe   0x00000008   Code   RO         1788    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000206   0x08000206   0x00000002   Code   RO         1888    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000208   0x08000208   0x00000000   Code   RO         1945    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000208   0x08000208   0x00000004   Code   RO         1946    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800020c   0x0800020c   0x00000006   Code   RO         1947    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000212   0x08000212   0x00000002   PAD
    0x08000214   0x08000214   0x00000040   Code   RO         1191    .text               startup_stm32f10x_hd.o
    0x08000254   0x08000254   0x0000002c   Code   RO         1547    .text               c_w.l(__2sprintf.o)
    0x08000280   0x08000280   0x00000078   Code   RO         1553    .text               c_w.l(_printf_dec.o)
    0x080002f8   0x080002f8   0x00000188   Code   RO         1572    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000480   0x08000480   0x0000003c   Code   RO         1577    .text               c_w.l(__0sscanf.o)
    0x080004bc   0x080004bc   0x0000014c   Code   RO         1579    .text               c_w.l(_scanf_int.o)
    0x08000608   0x08000608   0x00000080   Code   RO         1585    .text               c_w.l(strcmpv7m.o)
    0x08000688   0x08000688   0x00000006   Code   RO         1587    .text               c_w.l(heapauxi.o)
    0x0800068e   0x0800068e   0x000000b2   Code   RO         1660    .text               c_w.l(_printf_intcommon.o)
    0x08000740   0x08000740   0x00000030   Code   RO         1664    .text               c_w.l(_printf_char_common.o)
    0x08000770   0x08000770   0x0000000a   Code   RO         1666    .text               c_w.l(_sputc.o)
    0x0800077a   0x0800077a   0x0000001c   Code   RO         1722    .text               c_w.l(_chval.o)
    0x08000796   0x08000796   0x00000002   PAD
    0x08000798   0x08000798   0x0000002c   Code   RO         1724    .text               c_w.l(scanf_char.o)
    0x080007c4   0x080007c4   0x00000040   Code   RO         1726    .text               c_w.l(_sgetc.o)
    0x08000804   0x08000804   0x00000012   Code   RO         1803    .text               c_w.l(isspace.o)
    0x08000816   0x08000816   0x00000002   PAD
    0x08000818   0x08000818   0x00000374   Code   RO         1818    .text               c_w.l(_scanf.o)
    0x08000b8c   0x08000b8c   0x00000008   Code   RO         1832    .text               c_w.l(libspace.o)
    0x08000b94   0x08000b94   0x0000004a   Code   RO         1835    .text               c_w.l(sys_stackheap_outer.o)
    0x08000bde   0x08000bde   0x00000002   PAD
    0x08000be0   0x08000be0   0x00000010   Code   RO         1837    .text               c_w.l(rt_ctype_table.o)
    0x08000bf0   0x08000bf0   0x00000008   Code   RO         1842    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000bf8   0x08000bf8   0x00000012   Code   RO         1876    .text               c_w.l(exit.o)
    0x08000c0a   0x08000c0a   0x00000002   PAD
    0x08000c0c   0x08000c0c   0x0000000c   Code   RO         1959    .text               c_w.l(sys_exit.o)
    0x08000c18   0x08000c18   0x00000002   Code   RO         1974    .text               c_w.l(use_no_semi.o)
    0x08000c1a   0x08000c1a   0x00000000   Code   RO         1976    .text               c_w.l(indicate_semi.o)
    0x08000c1a   0x08000c1a   0x00000002   PAD
    0x08000c1c   0x08000c1c   0x00000002   Code   RO         1216    .text.BusFault_Handler  stm32f10x_it.o
    0x08000c1e   0x08000c1e   0x00000002   PAD
    0x08000c20   0x08000c20   0x00000002   Code   RO         1222    .text.DebugMon_Handler  stm32f10x_it.o
    0x08000c22   0x08000c22   0x00000002   PAD
    0x08000c24   0x08000c24   0x000000e6   Code   RO         1422    .text.EXTI0_IRQHandler  encoder.o
    0x08000d0a   0x08000d0a   0x00000002   PAD
    0x08000d0c   0x08000d0c   0x00000006   Code   RO         1434    .text.EXTI1_IRQHandler  encoder.o
    0x08000d12   0x08000d12   0x00000002   PAD
    0x08000d14   0x08000d14   0x0000006a   Code   RO         1424    .text.EXTI2_IRQHandler  encoder.o
    0x08000d7e   0x08000d7e   0x00000002   PAD
    0x08000d80   0x08000d80   0x0000000c   Code   RO          334    .text.EXTI_ClearITPendingBit  stm32f10x_exti.o
    0x08000d8c   0x08000d8c   0x0000001e   Code   RO          332    .text.EXTI_GetITStatus  stm32f10x_exti.o
    0x08000daa   0x08000daa   0x00000002   PAD
    0x08000dac   0x08000dac   0x00000022   Code   RO         1430    .text.Encoder_GetData  encoder.o
    0x08000dce   0x08000dce   0x00000002   PAD
    0x08000dd0   0x08000dd0   0x000000bc   Code   RO          458    .text.GPIO_Init     stm32f10x_gpio.o
    0x08000e8c   0x08000e8c   0x0000000a   Code   RO          462    .text.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x08000e96   0x08000e96   0x00000002   PAD
    0x08000e98   0x08000e98   0x00000004   Code   RO          472    .text.GPIO_ResetBits  stm32f10x_gpio.o
    0x08000e9c   0x08000e9c   0x00000004   Code   RO          470    .text.GPIO_SetBits  stm32f10x_gpio.o
    0x08000ea0   0x08000ea0   0x000000a0   Code   RO         1532    .text.Gimbal_AdjustToCenterTarget  gimbal.o
    0x08000f40   0x08000f40   0x00000044   Code   RO         1512    .text.Gimbal_Init   gimbal.o
    0x08000f84   0x08000f84   0x000000a4   Code   RO         1516    .text.Gimbal_UpdatePosition  gimbal.o
    0x08001028   0x08001028   0x00000002   Code   RO         1212    .text.HardFault_Handler  stm32f10x_it.o
    0x0800102a   0x0800102a   0x00000002   PAD
    0x0800102c   0x0800102c   0x0000000e   Code   RO         1481    .text.Left          servo.o
    0x0800103a   0x0800103a   0x00000002   PAD
    0x0800103c   0x0800103c   0x00000002   Code   RO         1214    .text.MemManage_Handler  stm32f10x_it.o
    0x0800103e   0x0800103e   0x00000002   PAD
    0x08001040   0x08001040   0x00000012   Code   RO         1499    .text.MotorControl_SetSpeed  motor_control.o
    0x08001052   0x08001052   0x00000002   PAD
    0x08001054   0x08001054   0x00000030   Code   RO         1501    .text.MotorControl_Update  motor_control.o
    0x08001084   0x08001084   0x00000002   Code   RO         1210    .text.NMI_Handler   stm32f10x_it.o
    0x08001086   0x08001086   0x00000002   PAD
    0x08001088   0x08001088   0x0000005e   Code   RO           84    .text.NVIC_Init     misc.o
    0x080010e6   0x080010e6   0x00000002   PAD
    0x080010e8   0x080010e8   0x0000000a   Code   RO         1453    .text.PID_SetTarget  pid.o
    0x080010f2   0x080010f2   0x00000002   PAD
    0x080010f4   0x080010f4   0x0000007c   Code   RO         1455    .text.PID_Update    pid.o
    0x08001170   0x08001170   0x00000164   Code   RO         1259    .text.PWM2_Init     pwm2.o
    0x080012d4   0x080012d4   0x00000090   Code   RO         1261    .text.PWM2_SetDutyCycle  pwm2.o
    0x08001364   0x08001364   0x000000a0   Code   RO         1240    .text.PWM_SetDutyCycle  pwm1.o
    0x08001404   0x08001404   0x00000002   Code   RO         1224    .text.PendSV_Handler  stm32f10x_it.o
    0x08001406   0x08001406   0x00000002   PAD
    0x08001408   0x08001408   0x00000018   Code   RO          665    .text.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08001420   0x08001420   0x00000018   Code   RO          663    .text.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08001438   0x08001438   0x0000008c   Code   RO          659    .text.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x080014c4   0x080014c4   0x0000000e   Code   RO         1483    .text.Right         servo.o
    0x080014d2   0x080014d2   0x00000002   PAD
    0x080014d4   0x080014d4   0x00000002   Code   RO         1220    .text.SVC_Handler   stm32f10x_it.o
    0x080014d6   0x080014d6   0x00000002   PAD
    0x080014d8   0x080014d8   0x00000064   Code   RO         1469    .text.Servo2_Init   servo.o
    0x0800153c   0x0800153c   0x00000044   Code   RO         1471    .text.Servo2_SetAngle  servo.o
    0x08001580   0x08001580   0x0000007c   Code   RO         1473    .text.Servo3_Init   servo.o
    0x080015fc   0x080015fc   0x00000044   Code   RO         1475    .text.Servo3_SetAngle  servo.o
    0x08001640   0x08001640   0x0000006a   Code   RO         1477    .text.Servo4_Init   servo.o
    0x080016aa   0x080016aa   0x00000002   PAD
    0x080016ac   0x080016ac   0x00000044   Code   RO         1479    .text.Servo4_SetAngle  servo.o
    0x080016f0   0x080016f0   0x00000064   Code   RO         1465    .text.Servo_Init    servo.o
    0x08001754   0x08001754   0x00000044   Code   RO         1467    .text.Servo_SetAngle  servo.o
    0x08001798   0x08001798   0x0000000c   Code   RO         1485    .text.Straight      servo.o
    0x080017a4   0x080017a4   0x00000002   Code   RO         1226    .text.SysTick_Handler  stm32f10x_it.o
    0x080017a6   0x080017a6   0x00000002   PAD
    0x080017a8   0x080017a8   0x00000110   Code   RO         1123    .text.SystemInit    system_stm32f10x.o
    0x080018b8   0x080018b8   0x0000004c   Code   RO         1426    .text.TIM6_IRQHandler  encoder.o
    0x08001904   0x08001904   0x00000028   Code   RO         1322    .text.TIM7_IRQHandler  timer7.o
    0x0800192c   0x0800192c   0x00000014   Code   RO          925    .text.TIM_ARRPreloadConfig  stm32f10x_tim.o
    0x08001940   0x08001940   0x00000006   Code   RO         1023    .text.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x08001946   0x08001946   0x00000002   PAD
    0x08001948   0x08001948   0x00000012   Code   RO          885    .text.TIM_Cmd       stm32f10x_tim.o
    0x0800195a   0x0800195a   0x00000002   PAD
    0x0800195c   0x0800195c   0x00000004   Code   RO         1013    .text.TIM_GetCounter  stm32f10x_tim.o
    0x08001960   0x08001960   0x00000018   Code   RO         1021    .text.TIM_GetITStatus  stm32f10x_tim.o
    0x08001978   0x08001978   0x00000070   Code   RO          855    .text.TIM_OC1Init   stm32f10x_tim.o
    0x080019e8   0x080019e8   0x0000000c   Code   RO          933    .text.TIM_OC1PreloadConfig  stm32f10x_tim.o
    0x080019f4   0x080019f4   0x00000074   Code   RO          857    .text.TIM_OC2Init   stm32f10x_tim.o
    0x08001a68   0x08001a68   0x0000000e   Code   RO          935    .text.TIM_OC2PreloadConfig  stm32f10x_tim.o
    0x08001a76   0x08001a76   0x00000002   PAD
    0x08001a78   0x08001a78   0x00000074   Code   RO          859    .text.TIM_OC3Init   stm32f10x_tim.o
    0x08001aec   0x08001aec   0x0000000c   Code   RO          937    .text.TIM_OC3PreloadConfig  stm32f10x_tim.o
    0x08001af8   0x08001af8   0x0000005c   Code   RO          861    .text.TIM_OC4Init   stm32f10x_tim.o
    0x08001b54   0x08001b54   0x0000000e   Code   RO          939    .text.TIM_OC4PreloadConfig  stm32f10x_tim.o
    0x08001b62   0x08001b62   0x00000002   PAD
    0x08001b64   0x08001b64   0x00000004   Code   RO          995    .text.TIM_SetCompare1  stm32f10x_tim.o
    0x08001b68   0x08001b68   0x00000004   Code   RO          997    .text.TIM_SetCompare2  stm32f10x_tim.o
    0x08001b6c   0x08001b6c   0x00000004   Code   RO          999    .text.TIM_SetCompare3  stm32f10x_tim.o
    0x08001b70   0x08001b70   0x00000006   Code   RO         1001    .text.TIM_SetCompare4  stm32f10x_tim.o
    0x08001b76   0x08001b76   0x00000002   PAD
    0x08001b78   0x08001b78   0x000000aa   Code   RO          853    .text.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08001c22   0x08001c22   0x00000002   PAD
    0x08001c24   0x08001c24   0x00000034   Code   RO         1228    .text.USART1_IRQHandler  stm32f10x_it.o
    0x08001c58   0x08001c58   0x00000084   Code   RO         1282    .text.USART1_Init   usart1.o
    0x08001cdc   0x08001cdc   0x00000054   Code   RO         1286    .text.USART1_ProcessReceivedData  usart1.o
    0x08001d30   0x08001d30   0x0000000e   Code   RO         1089    .text.USART_ClearITPendingBit  stm32f10x_usart.o
    0x08001d3e   0x08001d3e   0x00000002   PAD
    0x08001d40   0x08001d40   0x00000014   Code   RO         1043    .text.USART_Cmd     stm32f10x_usart.o
    0x08001d54   0x08001d54   0x0000000a   Code   RO         1083    .text.USART_GetFlagStatus  stm32f10x_usart.o
    0x08001d5e   0x08001d5e   0x00000002   PAD
    0x08001d60   0x08001d60   0x0000002a   Code   RO         1087    .text.USART_GetITStatus  stm32f10x_usart.o
    0x08001d8a   0x08001d8a   0x00000002   PAD
    0x08001d8c   0x08001d8c   0x0000002e   Code   RO         1045    .text.USART_ITConfig  stm32f10x_usart.o
    0x08001dba   0x08001dba   0x00000002   PAD
    0x08001dbc   0x08001dbc   0x000000b6   Code   RO         1035    .text.USART_Init    stm32f10x_usart.o
    0x08001e72   0x08001e72   0x00000002   PAD
    0x08001e74   0x08001e74   0x00000008   Code   RO         1061    .text.USART_ReceiveData  stm32f10x_usart.o
    0x08001e7c   0x08001e7c   0x00000008   Code   RO         1059    .text.USART_SendData  stm32f10x_usart.o
    0x08001e84   0x08001e84   0x00000002   Code   RO         1218    .text.UsageFault_Handler  stm32f10x_it.o
    0x08001e86   0x08001e86   0x00000002   PAD
    0x08001e88   0x08001e88   0x0000001a   Code   RO         1200    .text.main          main.o
    0x08001ea2   0x08001ea2   0x00000002   PAD
    0x08001ea4   0x08001ea4   0x000001bc   Code   RO         1278    .text.parse_received_data  usart1.o
    0x08002060   0x08002060   0x0000000e   Code   RO         1565    i._is_digit         c_w.l(__printf_wp.o)
    0x0800206e   0x0800206e   0x00000002   PAD
    0x08002070   0x08002070   0x0000002c   Code   RO         1893    locale$$code        c_w.l(lc_ctype_c.o)
    0x0800209c   0x0800209c   0x000000c4   Code   RO         1621    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x08002160   0x08002160   0x00000054   Code   RO         1627    x$fpl$fcmp          fz_ws.l(fcmp.o)
    0x080021b4   0x080021b4   0x00000018   Code   RO         1824    x$fpl$fcmpinf       fz_ws.l(fcmpi.o)
    0x080021cc   0x080021cc   0x00000184   Code   RO         1630    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x08002350   0x08002350   0x00000068   Code   RO         1742    x$fpl$feqf          fz_ws.l(feqf.o)
    0x080023b8   0x080023b8   0x00000036   Code   RO         1633    x$fpl$ffix          fz_ws.l(ffix.o)
    0x080023ee   0x080023ee   0x00000002   PAD
    0x080023f0   0x080023f0   0x00000030   Code   RO         1638    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x08002420   0x08002420   0x00000026   Code   RO         1637    x$fpl$ffltu         fz_ws.l(fflt_clz.o)
    0x08002446   0x08002446   0x00000002   PAD
    0x08002448   0x08002448   0x00000068   Code   RO         1744    x$fpl$fgeqf         fz_ws.l(fgeqf.o)
    0x080024b0   0x080024b0   0x00000068   Code   RO         1746    x$fpl$fleqf         fz_ws.l(fleqf.o)
    0x08002518   0x08002518   0x00000102   Code   RO         1643    x$fpl$fmul          fz_ws.l(fmul.o)
    0x0800261a   0x0800261a   0x0000008c   Code   RO         1748    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x080026a6   0x080026a6   0x0000000a   Code   RO         1750    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x080026b0   0x080026b0   0x000000ea   Code   RO         1623    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x0800279a   0x0800279a   0x00000000   Code   RO         1760    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x0800279a   0x0800279a   0x00000011   Data   RO         1573    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x080027ab   0x080027ab   0x00000004   Data   RO          684    .rodata.ADCPrescTable  stm32f10x_rcc.o
    0x080027af   0x080027af   0x00000010   Data   RO          683    .rodata.APBAHBPrescTable  stm32f10x_rcc.o
    0x080027bf   0x080027bf   0x00000011   Data   RO         1294    .rodata.str1.1      usart1.o
    0x080027d0   0x080027d0   0x00000020   Data   RO         2032    Region$$Table       anon$$obj.o
    0x080027f0   0x080027f0   0x00000110   Data   RO         1892    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08002900, Size: 0x000007d8, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08002900   0x00000002   Data   RW         1441    .data.EXTI2_IRQHandler.last_tim6_count  encoder.o
    0x20000002   0x08002902   0x00000002   PAD
    0x20000004   0x08002904   0x00000004   Data   RW         1127    .data.SystemCoreClock  system_stm32f10x.o
    0x20000008   0x08002908   0x00000004   Data   RW         1534    .data.currentPos    gimbal.o
    0x2000000c   0x0800290c   0x00000004   Data   RW         1439    .data.path          encoder.o
    0x20000010   0x08002910   0x00000002   Data   RW         1288    .data.redx          usart1.o
    0x20000012   0x08002912   0x00000002   Data   RW         1289    .data.redy          usart1.o
    0x20000014   0x08002914   0x00000004   Data   RW         1535    .data.targetPos     gimbal.o
    0x20000018        -       0x00000060   Zero   RW         1833    .bss                c_w.l(libspace.o)
    0x20000078        -       0x00000004   Zero   RW         1442    .bss.TIM6_IRQHandler.last_absolute_count  encoder.o
    0x2000007c        -       0x00000002   Zero   RW         1290    .bss.centerx        usart1.o
    0x2000007e        -       0x00000002   Zero   RW         1291    .bss.centery        usart1.o
    0x20000080        -       0x00000010   Zero   RW         1440    .bss.encoder        encoder.o
    0x20000090        -       0x00000028   Zero   RW         1503    .bss.motor          motor_control.o
    0x200000b8        -       0x00000002   Zero   RW         1250    .bss.pwmPeriod      pwm1.o
    0x200000ba        -       0x00000002   Zero   RW         1269    .bss.pwmPeriod      pwm2.o
    0x200000bc        -       0x00000100   Zero   RW         1296    .bss.rx_buffer      usart1.o
    0x200001bc        -       0x00000002   Zero   RW         1295    .bss.rx_index       usart1.o
    0x200001be   0x08002918   0x00000002   PAD
    0x200001c0        -       0x00000004   Zero   RW         1438    .bss.target_absolute_count  encoder.o
    0x200001c4        -       0x00000008   Zero   RW         1292    .bss.vertex_x       usart1.o
    0x200001cc        -       0x00000008   Zero   RW         1293    .bss.vertex_y       usart1.o
    0x200001d4   0x08002918   0x00000004   PAD
    0x200001d8        -       0x00000200   Zero   RW         1189    HEAP                startup_stm32f10x_hd.o
    0x200003d8        -       0x00000400   Zero   RW         1188    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       452          0          0          6         24       6982   encoder.o
       392          0          0          8          0       3737   gimbal.o
        26          0          0          0          0        686   main.o
        94          0          0          0          0       3148   misc.o
        66          0          0          0         40       2601   motor_control.o
       134          0          0          0          0       1367   pid.o
       160          4          0          0          2       5780   pwm1.o
       500         14          0          0          2       5138   pwm2.o
       742          0          0          0          0       4423   servo.o
        64         26        304          0       1536        804   startup_stm32f10x_hd.o
        42          0          0          0          0       2728   stm32f10x_exti.o
       206          0          0          0          0       5882   stm32f10x_gpio.o
        70          0          0          0          0       2004   stm32f10x_it.o
       188          0         20          0          0       8209   stm32f10x_rcc.o
       748          0          0          0          0      29595   stm32f10x_tim.o
       330          0          0          0          0       8541   stm32f10x_usart.o
       272          0          0          4          0       2539   system_stm32f10x.o
        40          0          0          0          0       4052   timer7.o
       660         84         17          4        278       6053   usart1.o

    ----------------------------------------------------------------------
      5252        <USER>        <GROUP>         24       1888     104269   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        66          0          0          2          6          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        60          8          0          0          0         84   __0sscanf.o
        44          6          0          0          0         84   __2sprintf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        94          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
       884          6          0          0          0        100   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
        64          0          0          0          0         84   _sgetc.o
        10          0          0          0          0         68   _sputc.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
         2          0          0          0          0          0   libinit.o
        20          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        44          8          0          0          0         84   scanf_char.o
       128          0          0          0          0         68   strcmpv7m.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
       430          8          0          0          0        168   faddsub_clz.o
        84          0          0          0          0        196   fcmp.o
        24          0          0          0          0         68   fcmpi.o
       388         76          0          0          0         96   fdiv.o
       104          4          0          0          0         84   feqf.o
        54          4          0          0          0         84   ffix.o
        86          0          0          0          0        136   fflt_clz.o
       104          4          0          0          0         84   fgeqf.o
       104          4          0          0          0         84   fleqf.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      4582        <USER>        <GROUP>          0         96       3288   Library Totals
        22          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2774         88        289          0         96       2052   c_w.l
      1786        108          0          0          0       1236   fz_ws.l

    ----------------------------------------------------------------------
      4582        <USER>        <GROUP>          0         96       3288   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      9834        324        662         24       1984     105513   Grand Totals
      9834        324        662         24       1984     105513   ELF Image Totals
      9834        324        662         24          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                10496 (  10.25kB)
    Total RW  Size (RW Data + ZI Data)              2008 (   1.96kB)
    Total ROM Size (Code + RO Data + RW Data)      10520 (  10.27kB)

==============================================================================

