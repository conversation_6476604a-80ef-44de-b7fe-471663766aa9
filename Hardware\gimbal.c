#include "gimbal.h"
#include "servo.h"
#include <math.h>
#include "usart1.h"

// ��һ����̨���ò���
#define GIMBAL_X_AXIS_SERVO  SERVO1  // X��ʹ�ö��1
#define GIMBAL_Y_AXIS_SERVO  SERVO2  // Y��ʹ�ö��2
#define GIMBAL_MOVE_STEP     1       // ÿ���ƶ��Ĳ���ֵ
#define GIMBAL_X_RANGE       100     // X�����귶Χ [-100, 100]
#define GIMBAL_Y_RANGE       100     // Y�����귶Χ [-100, 100]

// ��̨״̬
GimbalPosition currentPos = {165, 115};  // ��һ����̨��ǰλ��
GimbalPosition targetPos = {165, 115};   // ��һ����̨Ŀ��λ��

// Ŀ�����ĵ�����
#define TARGET_CENTER_X 165
#define TARGET_CENTER_Y 115

#define DEAD_BAND 2  // ������Χ

/**
  * @brief  ��̨��ʼ����������̨��
  * @note   ��ʼ�����ж�������ó�ʼλ��
  */
void Gimbal_Init(void) {
    // ��ʼ�����ж��
    Servo_Init();   // ���1
    Servo2_Init();  // ���2
    Servo3_Init();  // ���3
    Servo4_Init();  // ���4
    
    // ���ó�ʼλ�ã���̨1��
    currentPos.x = 0;
    currentPos.y = 0;
    targetPos.x = 0;
    targetPos.y = 0;
  
    // Ӧ�ó�ʼλ��
    Servo_SetAngle(90);   // ��̨1 X����
    Servo2_SetAngle(90);  // ��̨1 Y����
    Servo3_SetAngle(90);  // ��̨2 X����
    Servo4_SetAngle(90);  // ��̨2 Y����
}

// ============== ��һ����̨���� ==============

/**
  * @brief  ���õ�һ����̨Ŀ��λ��
  * @param  x: X��Ŀ������
  * @param  y: Y��Ŀ������
  */
void Gimbal_SetTargetPosition(int16_t x, int16_t y) {
    // �������귶Χ
    if (x < -GIMBAL_X_RANGE) x = -GIMBAL_X_RANGE;
    if (x > GIMBAL_X_RANGE) x = GIMBAL_X_RANGE;
    if (y < -GIMBAL_Y_RANGE) y = -GIMBAL_Y_RANGE;
    if (y > GIMBAL_Y_RANGE) y = GIMBAL_Y_RANGE;
    
    // ����Ŀ��λ��
    targetPos.x = x;
    targetPos.y = y;
}

/**
  * @brief  ���µ�һ����̨λ��
  * @note   ��Ҫ���ڵ��ô˺�������������ѭ���У�
  */
void Gimbal_UpdatePosition(void) {
    // X���ƶ�
    if (currentPos.x < targetPos.x) {
        currentPos.x += GIMBAL_MOVE_STEP;
        if (currentPos.x > targetPos.x) currentPos.x = targetPos.x;
    } else if (currentPos.x > targetPos.x) {
        currentPos.x -= GIMBAL_MOVE_STEP;
        if (currentPos.x < targetPos.x) currentPos.x = targetPos.x;
    }
    
    // Y���ƶ�
    if (currentPos.y < targetPos.y) {
        currentPos.y += GIMBAL_MOVE_STEP;
        if (currentPos.y > targetPos.y) currentPos.y = targetPos.y;
    } else if (currentPos.y > targetPos.y) {
        currentPos.y -= GIMBAL_MOVE_STEP;
        if (currentPos.y < targetPos.y) currentPos.y = targetPos.y;
    }
    
    // ������ת��Ϊ����Ƕ�
    uint8_t xAngle = 90 + (currentPos.x * 90) / GIMBAL_X_RANGE;
    uint8_t yAngle = 90 + (currentPos.y * 90) / GIMBAL_Y_RANGE;
    
    // ���ö���Ƕ�
    Servo_SetAngle(xAngle);   // X����
    Servo2_SetAngle(yAngle);  // Y����
}

/**
  * @brief  ��ȡ��һ����̨��ǰλ��
  * @retval ��ǰλ�ýṹ��
  */
GimbalPosition Gimbal_GetCurrentPosition(void) {
    return currentPos;
}

/**
  * @brief  ��ȡ��һ����̨Ŀ��λ��
  * @retval Ŀ��λ�ýṹ��
  */
GimbalPosition Gimbal_GetTargetPosition(void) {
    return targetPos;
}

// ��һ����̨����С�ƶ�����
void Gimbal_MoveXPlus(void) {
    GimbalPosition current = Gimbal_GetCurrentPosition();
    Gimbal_SetTargetPosition(current.x + GIMBAL_MOVE_STEP, current.y);
}

void Gimbal_MoveXMinus(void) {
    GimbalPosition current = Gimbal_GetCurrentPosition();
    Gimbal_SetTargetPosition(current.x - GIMBAL_MOVE_STEP, current.y);
}

void Gimbal_MoveYPlus(void) {
    GimbalPosition current = Gimbal_GetCurrentPosition();
    Gimbal_SetTargetPosition(current.x, current.y + GIMBAL_MOVE_STEP);
}

void Gimbal_MoveYMinus(void) {
    GimbalPosition current = Gimbal_GetCurrentPosition();
    Gimbal_SetTargetPosition(current.x, current.y - GIMBAL_MOVE_STEP);
}

/**
  * @brief  控制第一云台使红色激光瞄准目标位置
  * @note   需要在调用此函数后调用更新循环中，
  */
void Gimbal_AdjustToRedTarget(void) {
    // 获取当前红色目标位置
    extern int16_t redx, redy;  // 声明外部变量

    // 计算X轴差值
    int16_t diff_x = redx - TARGET_CENTER_X;

    // X轴方向控制（反向控制）
    if (diff_x > DEAD_BAND) {
        // 目标在图像右侧，云台左移
        Gimbal_MoveXMinus();
    } else if (diff_x < -DEAD_BAND) {
        // 目标在图像左侧，云台右移
        Gimbal_MoveXPlus();
    }

    // 计算Y轴差值
    int16_t diff_y = redy - TARGET_CENTER_Y;

    // Y轴方向控制（反向控制）
    if (diff_y > DEAD_BAND) {
        // 目标在图像下方，云台上移
        Gimbal_MoveYMinus();
    } else if (diff_y < -DEAD_BAND) {
        // 目标在图像上方，云台下移
        Gimbal_MoveYPlus();
    }
}

/**
  * @brief  控制第一云台追踪目标中心点（方案A）
  * @note   使用centerx,centery作为控制目标，直接设置目标位置
  */
void Gimbal_AdjustToCenterTarget(void) {
    // 获取当前目标中心点位置
    extern int16_t centerx, centery;  // 声明外部变量

    // 将图像坐标转换为云台坐标
    // 假设图像中心为(165, 115)，图像尺寸约为330x230
    // 云台坐标范围为[-100, 100]

    // 计算相对于图像中心的偏移
    int16_t offset_x = centerx - TARGET_CENTER_X;  // 相对于图像中心的X偏移
    int16_t offset_y = centery - TARGET_CENTER_Y;  // 相对于图像中心的Y偏移

    // 将图像偏移转换为云台坐标（比例缩放）
    // 图像半宽约165像素，对应云台范围100
    int16_t gimbal_x = -(offset_x * GIMBAL_X_RANGE) / 165;  // 反向控制
    int16_t gimbal_y = -(offset_y * GIMBAL_Y_RANGE) / 115;  // 反向控制

    // 限制云台坐标范围
    if (gimbal_x < -GIMBAL_X_RANGE) gimbal_x = -GIMBAL_X_RANGE;
    if (gimbal_x > GIMBAL_X_RANGE) gimbal_x = GIMBAL_X_RANGE;
    if (gimbal_y < -GIMBAL_Y_RANGE) gimbal_y = -GIMBAL_Y_RANGE;
    if (gimbal_y > GIMBAL_Y_RANGE) gimbal_y = GIMBAL_Y_RANGE;

    // 设置云台目标位置
    Gimbal_SetTargetPosition(gimbal_x, gimbal_y);

    // 调试输出（可选，用于验证）
    // 注意：频繁的串口输出可能影响性能，实际使用时可以注释掉
    /*
    extern void USART1_SendString(const char *str);
    char debug_buffer[64];
    sprintf(debug_buffer, "Gimbal target: (%d,%d)\r\n", gimbal_x, gimbal_y);
    USART1_SendString(debug_buffer);
    */
}




