# 舵机云台光线追踪系统修复报告

## 修复概述
采用方案A：使用目标中心点控制，统一使用centerx,centery作为控制目标。

## 主要修复内容

### 1. 串口中断处理修复
- **问题**：stm32f10x_it.c中缺少USART1_IRQHandler实现
- **修复**：
  - 在stm32f10x_it.c中添加正确的USART1_IRQHandler
  - 在usart1.c中创建USART1_ProcessReceivedData函数
  - 确保中断处理链路完整

### 2. 统一云台控制逻辑
- **问题**：主程序同时使用redx,redy和centerx,centery，造成控制目标混乱
- **修复**：
  - 创建新的Gimbal_AdjustToCenterTarget()函数
  - 主程序统一使用centerx,centery作为控制目标
  - 删除对redx,redy的依赖

### 3. 优化云台控制算法
- **问题**：图像坐标系和云台坐标系转换错误
- **修复**：
  - 正确的坐标系转换：图像坐标(0-330) → 云台坐标(-100到100)
  - 反向控制逻辑：目标在右侧时云台左移
  - 比例缩放：图像偏移按比例转换为云台角度

### 4. 添加调试功能
- **新增**：串口调试输出，便于问题定位
- **功能**：实时显示接收到的目标坐标和激光坐标

## 修复后的数据流

```
串口接收数据 → 数据解析 → 存储到centerx,centery → 
坐标系转换 → 设置云台目标位置 → 云台平滑移动 → 激光指向目标
```

## 关键改进

1. **坐标系统统一**：
   - 图像中心：(165, 115)
   - 云台坐标范围：[-100, 100]
   - 转换公式：gimbal_coord = -(image_offset * 100) / image_half_size

2. **控制逻辑优化**：
   - 使用反向控制：目标偏右时云台左移
   - 平滑移动：通过Gimbal_UpdatePosition()实现渐进式移动
   - 减少延时：从500ms降低到100ms，提高响应速度

3. **错误处理**：
   - 坐标范围限制
   - 缓冲区溢出保护
   - 中断标志正确清除

## 测试验证

### 预期效果
1. 串口能正确接收并解析A开头的数据（目标中心点）
2. 云台能根据目标位置进行相应移动
3. 激光点最终指向目标中心
4. 调试信息正确输出

### 测试数据示例
```
输入：A165,115  → 云台应保持中心位置
输入：A200,115  → 云台应向左移动
输入：A130,115  → 云台应向右移动
输入：A165,150  → 云台应向上移动
输入：A165,80   → 云台应向下移动
```

## 注意事项

1. **调试输出**：当前启用了调试输出，实际使用时可以注释掉以提高性能
2. **坐标校准**：如果图像尺寸不是330x230，需要调整转换比例
3. **移动速度**：可以通过调整GIMBAL_MOVE_STEP来改变云台移动速度
4. **死区设置**：DEAD_BAND=2，可以根据精度要求调整

## 文件修改清单

- `User/stm32f10x_it.c` - 添加USART1中断处理
- `System/usart1.c` - 修改数据处理逻辑，添加调试输出
- `System/usart1.h` - 添加新函数声明
- `Hardware/gimbal.c` - 新增目标中心点控制函数
- `Hardware/gimbal.h` - 添加新函数声明
- `User/main.c` - 修改主循环逻辑

修复完成！系统现在应该能够正确追踪目标中心点。
