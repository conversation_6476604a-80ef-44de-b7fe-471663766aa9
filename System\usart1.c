#include "usart1.h"
#include "uart_vision.h"
#include <string.h>
#include "uart_gimbal.h"
#include <stdio.h>  // ��������sscanf

// ȫ�ֱ�������
int16_t redx = 165, redy = 115;      // ��ɫ�������
int16_t centerx = 0, centery = 0;      // ������������ (A)
int16_t vertex_x[4] = {0};           // ���ζ���X���� (B)
int16_t vertex_y[4] = {0};           // ���ζ���Y���� (B)

// ���ջ�����
#define RX_BUFFER_SIZE 256
static uint8_t rx_buffer[RX_BUFFER_SIZE];
static uint16_t rx_index = 0;

// �������յ���������
void parse_received_data(char* line) {
    // ������ɫ������� (Cx,y)
    if (line[0] == 'C') {
        sscanf(line + 1, "%hd,%hd", &redx, &redy);
        // 调试输出
        USART1_SendString("DEBUG: Red laser at (");
        char buffer[32];
        sprintf(buffer, "%d,%d)\r\n", redx, redy);
        USART1_SendString(buffer);
    }
    // ���������������� (Ax,y)
    else if (line[0] == 'A') {
        sscanf(line + 1, "%hd,%hd", &centerx, &centery);
        // 调试输出
        USART1_SendString("DEBUG: Target center at (");
        char buffer[32];
        sprintf(buffer, "%d,%d)\r\n", centerx, centery);
        USART1_SendString(buffer);
    }
    // �������ζ������� (B,x1,y1,x2,y2,x3,y3,x4,y4)
    else if (line[0] == 'B') {
        sscanf(line + 2, "%hd,%hd,%hd,%hd,%hd,%hd,%hd,%hd",
               &vertex_x[0], &vertex_y[0],
               &vertex_x[1], &vertex_y[1],
               &vertex_x[2], &vertex_y[2],
               &vertex_x[3], &vertex_y[3]);
    }
}

// USART1��ʼ��
void USART1_Init(uint32_t baudrate) {
    GPIO_InitTypeDef GPIO_InitStruct;
    USART_InitTypeDef USART_InitStruct;
    NVIC_InitTypeDef NVIC_InitStruct;
    
    // 1. ʹ��ʱ��
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1 | RCC_APB2Periph_GPIOA | RCC_APB2Periph_AFIO, ENABLE);
    
    // 2. ����GPIO
    // TX (PA9) - �����������
    GPIO_InitStruct.GPIO_Pin = GPIO_Pin_9;
    GPIO_InitStruct.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_InitStruct.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStruct);
    
    // RX (PA10) - ��������
    GPIO_InitStruct.GPIO_Pin = GPIO_Pin_10;
    GPIO_InitStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_Init(GPIOA, &GPIO_InitStruct);
    
    // 3. ����USART����
    USART_InitStruct.USART_BaudRate = baudrate;
    USART_InitStruct.USART_WordLength = USART_WordLength_8b;
    USART_InitStruct.USART_StopBits = USART_StopBits_1;
    USART_InitStruct.USART_Parity = USART_Parity_No;
    USART_InitStruct.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStruct.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
    USART_Init(USART1, &USART_InitStruct);
    
    // 4. ���ý����ж�
    USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);
    
    // 5. ����NVIC
    NVIC_InitStruct.NVIC_IRQChannel = USART1_IRQn;
    NVIC_InitStruct.NVIC_IRQChannelPreemptionPriority = 0;
    NVIC_InitStruct.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStruct.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStruct);
    
    // 6. ʹ��USART
    USART_Cmd(USART1, ENABLE);
}

// ���͵����ַ�
void USART1_SendChar(char ch) {
    while(USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);
    USART_SendData(USART1, (uint8_t)ch);
}

// �����ַ���
void USART1_SendString(const char *str) {
    while(*str) {
        USART1_SendChar(*str++);
    }
}

// �޸ĺ���жϷ�����
void USART1_IRQHandler(void) {
    if (USART_GetITStatus(USART1, USART_IT_RXNE) != RESET) {
        uint8_t data = USART_ReceiveData(USART1);
        
        // �������з� (�����н���)
        if (data == '\n') {
            if (rx_index < RX_BUFFER_SIZE) {
                rx_buffer[rx_index] = '\0'; // �����ַ���������
                parse_received_data((char*)rx_buffer); // ��������
            }
            rx_index = 0; // ���û���������
        }
        // ������ͨ�ַ�
        else if (data != '\r') { // ���Իس���
            if (rx_index < RX_BUFFER_SIZE - 1) {
                rx_buffer[rx_index++] = data;
            } else {
                // �������������������
                rx_index = 0;
            }
        }
        
        // ����жϱ�־
        USART_ClearITPendingBit(USART1, USART_IT_RXNE);
    }
}

// 数据处理函数（供中断调用）
void USART1_ProcessReceivedData(uint8_t data) {
    // 检查换行符 (数据行结束)
    if (data == '\n') {
        if (rx_index < RX_BUFFER_SIZE) {
            rx_buffer[rx_index] = '\0'; // 添加字符串结束符
            parse_received_data((char*)rx_buffer); // 解析数据
        }
        rx_index = 0; // 重置缓冲区索引
    }
    // 处理普通字符
    else if (data != '\r') { // 忽略回车符
        if (rx_index < RX_BUFFER_SIZE - 1) {
            rx_buffer[rx_index++] = data;
        } else {
            // 缓冲区溢出，重置索引
            rx_index = 0;
        }
    }
}
