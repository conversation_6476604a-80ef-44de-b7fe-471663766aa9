#include "usart1.h"
#include "uart_vision.h"
#include <string.h>
#include "uart_gimbal.h"
#include <stdio.h>  // 添加头文件支持sscanf

// 全局变量定义
int16_t redx = 165, redy = 115;      // 红色激光坐标
int16_t centerx = 0, centery = 0;      // 目标中心点坐标 (A)
int16_t vertex_x[4] = {0};           // 四边形顶点X坐标 (B)
int16_t vertex_y[4] = {0};           // 四边形顶点Y坐标 (B)

// 接收缓冲区
#define RX_BUFFER_SIZE 256
static uint8_t rx_buffer[RX_BUFFER_SIZE];
static uint16_t rx_index = 0;

// 解析接收到的数据行
void parse_received_data(char* line) {
    // 解析红色激光坐标 (Cx,y)
    if (line[0] == 'C') {
        sscanf(line + 1, "%hd,%hd", &redx, &redy);
        // 调试输出
        USART1_SendString("DEBUG: Red laser at (");
        char buffer[32];
        sprintf(buffer, "%d,%d)\r\n", redx, redy);
        USART1_SendString(buffer);
    }
    // 解析目标中心点坐标 (Ax,y)
    else if (line[0] == 'A') {
        sscanf(line + 1, "%hd,%hd", &centerx, &centery);
        // 调试输出
        USART1_SendString("DEBUG: Target center at (");
        char buffer[32];
        sprintf(buffer, "%d,%d)\r\n", centerx, centery);
        USART1_SendString(buffer);
    }
    // 解析四边形顶点坐标 (B,x1,y1,x2,y2,x3,y3,x4,y4)
    else if (line[0] == 'B') {
        sscanf(line + 2, "%hd,%hd,%hd,%hd,%hd,%hd,%hd,%hd",
               &vertex_x[0], &vertex_y[0],
               &vertex_x[1], &vertex_y[1],
               &vertex_x[2], &vertex_y[2],
               &vertex_x[3], &vertex_y[3]);
    }
}

// USART1初始化
void USART1_Init(uint32_t baudrate) {
    GPIO_InitTypeDef GPIO_InitStruct;
    USART_InitTypeDef USART_InitStruct;
    NVIC_InitTypeDef NVIC_InitStruct;

    // 1. 使能时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1 | RCC_APB2Periph_GPIOA | RCC_APB2Periph_AFIO, ENABLE);

    // 2. 配置GPIO
    // TX (PA9) - 复用推挽输出
    GPIO_InitStruct.GPIO_Pin = GPIO_Pin_9;
    GPIO_InitStruct.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_InitStruct.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStruct);

    // RX (PA10) - 浮空输入
    GPIO_InitStruct.GPIO_Pin = GPIO_Pin_10;
    GPIO_InitStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_Init(GPIOA, &GPIO_InitStruct);

    // 3. 配置USART参数
    USART_InitStruct.USART_BaudRate = baudrate;
    USART_InitStruct.USART_WordLength = USART_WordLength_8b;
    USART_InitStruct.USART_StopBits = USART_StopBits_1;
    USART_InitStruct.USART_Parity = USART_Parity_No;
    USART_InitStruct.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStruct.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
    USART_Init(USART1, &USART_InitStruct);

    // 4. 配置接收中断
    USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);

    // 5. 配置NVIC
    NVIC_InitStruct.NVIC_IRQChannel = USART1_IRQn;
    NVIC_InitStruct.NVIC_IRQChannelPreemptionPriority = 0;
    NVIC_InitStruct.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStruct.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStruct);

    // 6. 使能USART
    USART_Cmd(USART1, ENABLE);
}

// 发送单个字符
void USART1_SendChar(char ch) {
    while(USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);
    USART_SendData(USART1, (uint8_t)ch);
}

// 发送字符串
void USART1_SendString(const char *str) {
    while(*str) {
        USART1_SendChar(*str++);
    }
}

// 注意：USART1_IRQHandler现在在stm32f10x_it.c中实现

// 数据处理函数（供中断调用）
void USART1_ProcessReceivedData(uint8_t data) {
    // 检查换行符 (数据行结束)
    if (data == '\n') {
        if (rx_index < RX_BUFFER_SIZE) {
            rx_buffer[rx_index] = '\0'; // 添加字符串结束符
            parse_received_data((char*)rx_buffer); // 解析数据
        }
        rx_index = 0; // 重置缓冲区索引
    }
    // 处理普通字符
    else if (data != '\r') { // 忽略回车符
        if (rx_index < RX_BUFFER_SIZE - 1) {
            rx_buffer[rx_index++] = data;
        } else {
            // 缓冲区溢出，重置索引
            rx_index = 0;
        }
    }
}