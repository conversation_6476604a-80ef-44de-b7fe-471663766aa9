#!/usr/bin/env python3
"""
舵机云台测试脚本
用于测试修复后的云台控制系统
"""

import serial
import time
import sys

def test_gimbal_control():
    """测试云台控制功能"""
    
    # 配置串口（根据实际情况修改端口）
    try:
        ser = serial.Serial(
            port='COM3',  # 根据实际情况修改
            baudrate=115200,
            timeout=1
        )
        print("串口连接成功")
    except Exception as e:
        print(f"串口连接失败: {e}")
        return
    
    # 测试数据
    test_cases = [
        ("A165,115", "中心位置测试"),
        ("A200,115", "目标右侧测试（云台应左移）"),
        ("A130,115", "目标左侧测试（云台应右移）"),
        ("A165,150", "目标下方测试（云台应上移）"),
        ("A165,80",  "目标上方测试（云台应下移）"),
        ("A220,150", "右下角测试"),
        ("A110,80",  "左上角测试"),
    ]
    
    print("开始云台控制测试...")
    print("=" * 50)
    
    for i, (command, description) in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {description}")
        print(f"发送命令: {command}")
        
        # 发送测试命令
        ser.write((command + '\n').encode())
        
        # 等待并读取响应
        time.sleep(0.5)
        response = ""
        while ser.in_waiting > 0:
            response += ser.read(ser.in_waiting).decode('utf-8', errors='ignore')
        
        if response:
            print(f"系统响应: {response.strip()}")
        else:
            print("无响应")
        
        # 等待云台移动
        print("等待云台移动...")
        time.sleep(3)
        
        input("按回车键继续下一个测试...")
    
    # 回到中心位置
    print("\n回到中心位置...")
    ser.write(b'A165,115\n')
    time.sleep(2)
    
    ser.close()
    print("\n测试完成！")

def manual_test():
    """手动测试模式"""
    try:
        ser = serial.Serial(
            port='COM3',  # 根据实际情况修改
            baudrate=115200,
            timeout=1
        )
        print("手动测试模式启动")
        print("输入格式: Ax,y (例如: A200,115)")
        print("输入 'quit' 退出")
    except Exception as e:
        print(f"串口连接失败: {e}")
        return
    
    while True:
        command = input("\n请输入命令: ").strip()
        
        if command.lower() == 'quit':
            break
        
        if not command.startswith('A') or ',' not in command:
            print("格式错误！请使用 Ax,y 格式")
            continue
        
        # 发送命令
        ser.write((command + '\n').encode())
        print(f"已发送: {command}")
        
        # 读取响应
        time.sleep(0.5)
        response = ""
        while ser.in_waiting > 0:
            response += ser.read(ser.in_waiting).decode('utf-8', errors='ignore')
        
        if response:
            print(f"系统响应: {response.strip()}")
    
    ser.close()
    print("手动测试结束")

if __name__ == "__main__":
    print("舵机云台测试程序")
    print("1. 自动测试")
    print("2. 手动测试")
    
    choice = input("请选择测试模式 (1/2): ").strip()
    
    if choice == "1":
        test_gimbal_control()
    elif choice == "2":
        manual_test()
    else:
        print("无效选择")
