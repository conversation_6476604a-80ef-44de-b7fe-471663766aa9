.\objects\motor_control_1.o: Hardware\motor_control.c
.\objects\motor_control_1.o: Hardware\motor_control.h
.\objects\motor_control_1.o: .\Start\stm32f10x.h
.\objects\motor_control_1.o: .\Start\core_cm3.h
.\objects\motor_control_1.o: D:\Keil5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\motor_control_1.o: .\Start\system_stm32f10x.h
.\objects\motor_control_1.o: .\User\stm32f10x_conf.h
.\objects\motor_control_1.o: .\Library\stm32f10x_adc.h
.\objects\motor_control_1.o: .\Start\stm32f10x.h
.\objects\motor_control_1.o: .\Library\stm32f10x_bkp.h
.\objects\motor_control_1.o: .\Library\stm32f10x_can.h
.\objects\motor_control_1.o: .\Library\stm32f10x_cec.h
.\objects\motor_control_1.o: .\Library\stm32f10x_crc.h
.\objects\motor_control_1.o: .\Library\stm32f10x_dac.h
.\objects\motor_control_1.o: .\Library\stm32f10x_dbgmcu.h
.\objects\motor_control_1.o: .\Library\stm32f10x_dma.h
.\objects\motor_control_1.o: .\Library\stm32f10x_exti.h
.\objects\motor_control_1.o: .\Library\stm32f10x_flash.h
.\objects\motor_control_1.o: .\Library\stm32f10x_fsmc.h
.\objects\motor_control_1.o: .\Library\stm32f10x_gpio.h
.\objects\motor_control_1.o: .\Library\stm32f10x_i2c.h
.\objects\motor_control_1.o: .\Library\stm32f10x_iwdg.h
.\objects\motor_control_1.o: .\Library\stm32f10x_pwr.h
.\objects\motor_control_1.o: .\Library\stm32f10x_rcc.h
.\objects\motor_control_1.o: .\Library\stm32f10x_rtc.h
.\objects\motor_control_1.o: .\Library\stm32f10x_sdio.h
.\objects\motor_control_1.o: .\Library\stm32f10x_spi.h
.\objects\motor_control_1.o: .\Library\stm32f10x_tim.h
.\objects\motor_control_1.o: .\Library\stm32f10x_usart.h
.\objects\motor_control_1.o: .\Library\stm32f10x_wwdg.h
.\objects\motor_control_1.o: .\Library\misc.h
.\objects\motor_control_1.o: .\System\pid.h
.\objects\motor_control_1.o: .\System\pwm1.h
.\objects\motor_control_1.o: Hardware\encoder.h
